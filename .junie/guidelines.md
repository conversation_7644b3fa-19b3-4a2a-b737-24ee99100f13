# Finema UI Kit Project Guidelines

## Project Overview
Finema UI Kit (@finema/core) is a Nuxt-based UI component library that provides reusable UI components for Finema applications. The project is built with TypeScript and follows modern frontend development practices.

## Project Structure
- **src/**: Source code for the UI kit
  - **runtime/components/**: UI components (Dialog, FlexDeck, Form, Table, etc.)
  - **runtime/composables/**: Vue composables for reusable logic
  - **runtime/utils/**: Utility functions
  - **runtime/theme/**: Theming and styling
  - **runtime/types/**: TypeScript type definitions
  - **styles/**: Global CSS/styling files
- **playground/**: Demo application for testing and showcasing components
  - **pages/**: Example pages demonstrating component usage
- **test/**: Test files using Vitest
- **docs/**: Documentation

## Development Guidelines
1. **Code Style**: Follow the ESLint configuration in the project. Run `bun run lint` to check for issues and `bun run lint:fix` to automatically fix them.

2. **Testing**: 
   - Write tests for new components and features
   - Run tests with `bun run test` before submitting changes
   - For watching tests during development, use `bun run test:watch`

3. **Development Workflow**:
   - Use `bun run dev` to run the playground in development mode
   - Use `bun run dev:prepare` to generate type stubs
   - Use `bun run dev:build` to build the playground

4. **Component Guidelines**:
   - Keep components focused on a single responsibility
   - Ensure components are properly typed with TypeScript
   - Follow the existing component structure and patterns
   - Document component props and usage

## When Working with Junie
1. Junie should run tests to verify changes with `run_test test/basic.test.ts`
2. For significant changes, Junie should build the project with `build` command
3. Junie should ensure code follows the project's ESLint rules
4. When modifying components, Junie should verify they work correctly in the playground
