# Finema UI Kit - Architecture

## System Architecture
The Finema UI Kit is built as a **Nuxt module** that provides a comprehensive component library with auto-import capabilities. The architecture follows modern Vue 3 and Nuxt 3 patterns with TypeScript throughout.

## Core Module Structure

### Entry Point
- **[`src/module.ts`](src/module.ts)**: Main Nuxt module definition that configures auto-imports, dependencies, and theme integration

### Runtime System
- **[`src/runtime/`](src/runtime/)**: All runtime code that gets bundled with consuming applications
- **[`src/runtime/plugin.ts`](src/runtime/plugin.ts)**: Runtime plugin for initialization
- **[`src/runtime/styles/main.css`](src/runtime/styles/main.css)**: Core CSS styles

### Configuration
- **[`src/core.config.ts`](src/core.config.ts)**: Default configuration values for dates, formats, and system settings
- **[`src/options.ts`](src/options.ts)**: Nuxt app and runtime configuration options

## Component Architecture

### Component Organization
```
src/runtime/components/
├── App.vue                    # Application wrapper component
├── Empty.vue                  # Empty state component
├── Image.vue                  # Image with loading/error states
├── Loader.vue                 # Loading indicator
├── TeleportSafe.vue          # Safe teleportation wrapper
├── DevToolsWindow/           # Development tools
├── Dialog/                   # Modal dialog system
├── FlexDeck/                 # Flexible deck layout
├── Form/                     # Complete form system
├── Log/                      # Logging components
└── Table/                    # Data table components
```

### Form System Architecture
The form system is the most complex component with a hierarchical structure:

#### Core Form Components
- **[`Form/index.vue`](src/runtime/components/Form/index.vue)**: Main form wrapper
- **[`Form/Fields.vue`](src/runtime/components/Form/Fields.vue)**: Dynamic field renderer
- **[`Form/FieldWrapper.vue`](src/runtime/components/Form/FieldWrapper.vue)**: Individual field wrapper
- **[`Form/types.ts`](src/runtime/components/Form/types.ts)**: Form type definitions with 20+ input types

#### Input Components
- Text inputs: [`InputText/`](src/runtime/components/Form/InputText/), [`InputTextarea/`](src/runtime/components/Form/InputTextarea/), [`InputSearch/`](src/runtime/components/Form/InputSearch/)
- Selection: [`InputSelect/`](src/runtime/components/Form/InputSelect/), [`InputSelectMultiple/`](src/runtime/components/Form/InputSelectMultiple/), [`InputRadio/`](src/runtime/components/Form/InputRadio/), [`InputCheckbox/`](src/runtime/components/Form/InputCheckbox/)
- Specialized: [`InputNumber/`](src/runtime/components/Form/InputNumber/), [`InputToggle/`](src/runtime/components/Form/InputToggle/), [`InputDateTime/`](src/runtime/components/Form/InputDateTime/), [`InputUploadDropzoneAuto/`](src/runtime/components/Form/InputUploadDropzoneAuto/), [`InputWYSIWYG/`](src/runtime/components/Form/InputWYSIWYG/)

## Composables Architecture

### Core Composables
Located in [`src/runtime/composables/`](src/runtime/composables/):

#### Data Loading
- **[`usePageLoader`](src/runtime/composables/loaderPage.ts)**: Complete CRUD operations with pagination
- **[`useListLoader`](src/runtime/composables/loaderList.ts)**: List data loading
- **[`useObjectLoader`](src/runtime/composables/loaderObject.ts)**: Single object operations

#### UI Management
- **[`useDialog`](src/runtime/composables/useDialog.ts)**: Modal dialog management
- **[`useNotification`](src/runtime/composables/useNotification.ts)**: Toast notification system
- **[`useFlexDeck`](src/runtime/composables/useFlexDeck.ts)**: Flexible layout management
- **[`useTable`](src/runtime/composables/useTable.ts)**: Table configuration and state

#### Form Utilities
- **[`useForm`](src/runtime/composables/useForm.ts)**: Form helpers and field management
- **[`useUpload`](src/runtime/composables/useUpload.ts)**: File upload functionality

## Helper & Utility Architecture

### Helper Classes
Located in [`src/runtime/utils/`](src/runtime/utils/) with comprehensive test coverage:

#### Data Manipulation
- **[`ArrayHelper`](src/runtime/utils/ArrayHelper.ts)**: Array transformations and options creation
- **[`ObjectHelper`](src/runtime/utils/ObjectHelper.ts)**: Object manipulation and status management
- **[`StringHelper`](src/runtime/utils/StringHelper.ts)**: String operations, URL joining, error handling
- **[`NumberHelper`](src/runtime/utils/NumberHelper.ts)**: Number formatting and currency conversion
- **[`TimeHelper`](src/runtime/utils/TimeHelper.ts)**: Date/time formatting with Thai localization support

#### File & Data Processing
- **[`FileHelper`](src/runtime/utils/FileHelper.ts)**: File operations and Base64 conversion
- **[`ParamHelper`](src/runtime/utils/ParamHelper.ts)**: Parameter handling and boolean utilities

#### Custom Lodash
- **[`lodash.ts`](src/runtime/utils/lodash.ts)**: Custom lightweight lodash implementation

### API Helpers
Located in [`src/runtime/helpers/`](src/runtime/helpers/):
- **[`apiBaseHelper.ts`](src/runtime/helpers/apiBaseHelper.ts)**: Base API functionality
- **[`apiListHelper.ts`](src/runtime/helpers/apiListHelper.ts)**: List API operations
- **[`apiObjectHelper.ts`](src/runtime/helpers/apiObjectHelper.ts)**: Object API operations
- **[`apiPageHelper.ts`](src/runtime/helpers/apiPageHelper.ts)**: Paginated API operations
- **[`componentHelper.ts`](src/runtime/helpers/componentHelper.ts)**: Component utilities

## Theme & Styling Architecture

### Theme System
Located in [`src/runtime/theme/`](src/runtime/theme/):
- **[`index.ts`](src/runtime/theme/index.ts)**: Main theme export
- Component-specific themes: [`button.ts`](src/runtime/theme/button.ts), [`form.ts`](src/runtime/theme/form.ts), [`table.ts`](src/runtime/theme/table.ts), etc.
- **[`icons.ts`](src/runtime/theme/icons.ts)**: Icon configuration

### Integration Libraries
- **Tailwind CSS**: Primary styling framework
- **@nuxt/ui**: Base UI framework integration
- **@iconify**: Icon system with Heroicons and Phosphor icons

## Type System Architecture

### Type Organization
- **[`src/runtime/types/index.ts`](src/runtime/types/index.ts)**: Main type exports
- **[`src/runtime/types/lib.ts`](src/runtime/types/lib.ts)**: Library-specific types
- **[`src/runtime/types/common.ts`](src/runtime/types/common.ts)**: Common type definitions

### Component Types
Each complex component has its own type definitions:
- Form input types in individual component folders
- Table types in [`src/runtime/components/Table/types.ts`](src/runtime/components/Table/types.ts)
- FlexDeck types in [`src/runtime/components/FlexDeck/types.ts`](src/runtime/components/FlexDeck/types.ts)

## Integration Architecture

### External Dependencies
The module automatically installs and configures:
- **[@nuxt/ui](src/module.ts:92)**: Base UI framework
- **[@pinia/nuxt](src/module.ts:107)**: State management
- **[@vee-validate/nuxt](src/module.ts:108)**: Form validation
- **[nuxt-lodash](src/module.ts:113)**: Utility functions

### Rich Text Editor
- **TipTap**: WYSIWYG editor with extensions for images, links, YouTube embeds
- **@vuepic/vue-datepicker**: Date/time picker component

### File Handling
- **Maska**: Input masking
- **Axios**: HTTP client for API requests

## Development Architecture

### Playground System
- **[`playground/`](playground/)**: Complete testing environment
- **[`playground/pages/form.vue`](playground/pages/form.vue)**: Comprehensive form demonstration
- Mock API endpoints in [`playground/server/api/mock/`](playground/server/api/mock/)

### Testing Framework
- **Vitest**: Unit testing for utilities and helpers
- **@nuxt/test-utils**: E2E testing setup
- Comprehensive test coverage for all utility classes

### Build System
- **@nuxt/module-builder**: Module building and packaging
- **TypeScript**: Full type safety throughout
- **ESLint**: Code quality and consistency
- **Husky**: Git hooks for quality checks

## Critical Implementation Paths

### Form Creation Flow
1. Define form schema with [`valibot`](src/module.ts:174)
2. Create field definitions using [`INPUT_TYPES`](src/runtime/components/Form/types.ts:17)
3. Use [`FormFields`](src/runtime/components/Form/Fields.vue) component to render
4. Handle submission with [`vee-validate`](src/module.ts:108) integration

### Data Loading Pattern
1. Use appropriate loader composable (`usePageLoader`, `useListLoader`, `useObjectLoader`)
2. Configure API helpers for backend communication
3. Handle loading states and error management
4. Display data using Table or custom components

### Theme Customization
1. Override theme values in [`src/runtime/theme/`](src/runtime/theme/)
2. Extend or modify component-specific styling
3. Integrate with consuming application's Tailwind configuration
