# Changelog


## v2.29.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.28.0...v2.29.0)

### 🚀 Enhancements

- Prevent default action on Enter key if no suggestion is selected (ebcb8f0)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.28.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.27.0...v2.28.0)

### 🚀 Enhancements

- Emit 'selected' event with the chosen suggestion in InputText component (bc56808)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.27.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.8...v2.27.0)

### 🚀 Enhancements

- Add 'selected' event to InputText component for suggestion selection (ca75ff4)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.8

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.7...v2.26.8)

### 🩹 Fixes

- Change COLUMN_TYPES to a regular enum for better compatibility (5fc87a8)

### 💅 Refactors

- Remove isSimplePagination option from IBaseTableOptions (af50139)
- Remove isSimplePagination option from tableOptions (4a3d9aa)

### 🏡 Chore

- Update tiptap dependencies to version 3.0.7 and nuxt/module-builder to 1.0.2 (b359a41)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.7

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.6...v2.26.7)

### 🩹 Fixes

- Update buttonTheme to use an array for base slots (279fb93)

### 🏡 Chore

- Update dependencies and improve TypeScript configuration (fdaa511)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](https://github.com/pskclub))

## v2.26.6

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.5...v2.26.6)

### 🩹 Fixes

- Update addRun and updateRun to accept partial payloads (b1ac833)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.5

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.4...v2.26.5)

### 🩹 Fixes

- Restructure Loader component template for improved readability (7e08f09)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.4

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.3...v2.26.4)

### 🩹 Fixes

- Ensure icon name defaults to an empty string if not provided (96d8882)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.2...v2.26.3)

### 🩹 Fixes

- Simplify loading and close dialog methods in useDialog (770fdce)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.1...v2.26.2)

### 🩹 Fixes

- Refactor loading dialog handling and ensure proper modal closure (52fabed)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.26.0...v2.26.1)

### 🩹 Fixes

- Update titleTemplate to use coreConfig.site_name directly (b258dec)
- Add 'form-data' to build transpile list and include additional dependencies in vite optimizeDeps (51ec2f1)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.26.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.25.0...v2.26.0)

### 🚀 Enhancements

- Add 'isEmpty' method to lodash integration (b2b995a)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.25.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.24.0...v2.25.0)

### 🚀 Enhancements

- Integrate lodash-es and update module imports (ed45f90)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.24.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.23.1...v2.24.0)

### 🚀 Enhancements

- Add loading dialog functionality and update dialog theme (06bb476)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.23.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.23.0...v2.23.1)

### 💅 Refactors

- Remove unused watch and streamline upload state handling (b434e81)

### 🎨 Styles

- Improve template formatting for better readability (d0c389c)

### ❤️ Contributors

- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.23.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.22.0...v2.23.0)

### 🚀 Enhancements

- File drop auto (47425a2)

### ❤️ Contributors

- Sea <<EMAIL>>

## v2.22.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.21.1...v2.22.0)

### 🚀 Enhancements

- **Table:** Bind ui prop to Table component for enhanced customization (7556b04)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](https://github.com/pskclub))

## v2.21.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.21.0...v2.21.1)

### 💅 Refactors

- Improve code formatting and consistency across multiple files (49aa80e)
- Remove unused type export from index.ts (de758e0)

### 🏡 Chore

- Update dependencies and devDependencies to latest versions (ee8388e)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](https://github.com/pskclub))
- PskCluB ([@pskclub](https://github.com/pskclub))

## v2.21.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.20.0...v2.21.0)

### 🚀 Enhancements

- Add NumberHelper utility class for number formatting and conversion (1f8f8db)
- Add comprehensive memory bank documentation for Finema UI Kit (5830319)
- Enhance NumberHelper to support dynamic currency formatting and add comprehensive tests (64be1ea)

### 💅 Refactors

- Simplify file progress calculations by removing NumberHelper dependency (c3fdecc)
- Rename isPreventRouteChange to isRouteChange for consistency across components (9b663cb)

### 🏡 Chore

- Update eslint-plugin-better-tailwindcss to version 3.3.0 (6754e8a)
- Remove outdated memory bank documentation files (bb925aa)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))
- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.20.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.19.1...v2.20.0)

### 🚀 Enhancements

- Input search (6f22f1b)

### 💅 Refactors

- Update ESLint configuration and dependencies (9cbf65f)
- Organize form fields into grouped sections for better readability (3177728)

### ❤️ Contributors

- Sea <<EMAIL>>
- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.19.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.19.0...v2.19.1)

### 💅 Refactors

- Update icon configuration and theme usage across dialog and upload dropzone components (8403c71)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.19.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.18.3...v2.19.0)

### 🚀 Enhancements

- Add search input component with debounce and clear functionality (b86765f)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.18.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.18.2...v2.18.3)

### 💅 Refactors

- Remove unused watch for request parameters in Table component (9add082)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.18.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.18.1...v2.18.2)

### 🩹 Fixes

- Ensure limit and page parameters are numbers in apiFetchHelper (fccff46)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.18.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.18.0...v2.18.1)

### 🩹 Fixes

- Change watch immediate option to false for InputDateTime and InputDateTimeRange components (9b51cdf)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.18.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.17.4...v2.18.0)

### 🚀 Enhancements

- Enhance date and datetime handling in form components (5ecdf27)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.17.4

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.17.3...v2.17.4)

### 🩹 Fixes

- Include page in options extraction for apiFetchHelper (a15a488)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.17.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.17.2...v2.17.3)

### 🩹 Fixes

- Clean up query parameters by removing empty values in navigation (8da978d)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.17.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.17.1...v2.17.2)

### 🩹 Fixes

- Prevent route change when navigating pages in table component (d85ceec)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.17.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.17.0...v2.17.1)

### 🩹 Fixes

- Spread theme array correctly in module options (900c51a)
- Correct syntax for merging theme into appConfig.ui (1429313)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.17.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.16.1...v2.17.0)

### 🚀 Enhancements

- Export IStatus type from lib in index.ts (d7c3e18)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.16.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.16.0...v2.16.1)

### 💅 Refactors

- Remove unused core types and interfaces from index.ts (b78b023)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.16.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.15.3...v2.16.0)

### 🚀 Enhancements

- Add engines field to specify Node.js version requirement (9b1c16b)
- Implement auto-import functionality for TypeScript interfaces and types in the Nuxt module (8ad121f)

### 🩹 Fixes

- Remove unused sample text field example from types-demo.vue (97ea709)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))
- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.15.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.15.2...v2.15.3)

### 🩹 Fixes

- Remove duplicate @tailwindcss/typography dependency from devDependencies (96c92f9)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.15.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.15.1...v2.15.2)

### 🩹 Fixes

- Update v-for loop to iterate over rawData instead of innerRawData (062e928)

### 🏡 Chore

- **release:** V2.15.1 (e5985c9)

### ✅ Tests

- Verify ICU4C fix after Node.js upgrade (1193e84)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.15.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.15.1...v2.15.1)

### 🩹 Fixes

- Update v-for loop to iterate over rawData instead of innerRawData (062e928)

### ✅ Tests

- Verify ICU4C fix after Node.js upgrade (1193e84)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.15.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.15.0...v2.15.1)

### 🩹 Fixes

- Update v-for loop to iterate over rawData instead of innerRawData (e5b7430)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.15.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.14.1...v2.15.0)

### 🚀 Enhancements

- Add radio input component for theme and plan selection (3baf4bf)
- Add radio group theme for consistent styling (f3b4221)
- Add WYSIWYG editor component with toolbar and image upload support (6cff352)

### 🩹 Fixes

- Adjust padding in WYSIWYG editor for improved layout (0853798)
- Disable YouTube option in WYSIWYG editor toolbar (0550cc4)
- Render WYSIWYG editor fallback content using v-html for better display (1a8dbbe)

### 💅 Refactors

- Remove unused icon and avatar properties from RadioOption type (e84a9b8)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.14.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.14.0...v2.14.1)

### 🩹 Fixes

- Reorder class attributes for consistency in various components (e6cd07f)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.14.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.13.0...v2.14.0)

### 🚀 Enhancements

- Add file size validation and watch for value changes in upload dropzone (5ebc3d4)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.13.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.12.3...v2.13.0)

### 🚀 Enhancements

- Implement Cline's Memory Bank documentation structure and core files (e772113)
- Enhance file upload component with new states and UI elements (a9f2c61)
- Update upload dropzone component to support additional response properties (51b6df8)
- Update file upload component to support additional file types and improve validation (15467d2)
- Enhance upload state management and update dropzone theme styles (85dc0bf)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.12.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.12.2...v2.12.3)

### 🩹 Fixes

- Rename isHideBottomPagination to isHidePagination in table components (e87dd4d)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.12.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.12.1...v2.12.2)

### 🩹 Fixes

- Bind locale prop to UApp component using props (910b9a3)
- Update type definition for props in App component (4e03d03)
- Correct import path for AppProps type definition in App component (06c8d0d)
- Bind locale and toaster props directly in UApp component (a1ec66b)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.12.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.12.0...v2.12.1)

### 🩹 Fixes

- Update type definitions for loader functions in IUsePageLoader interface (ba49eb7)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.12.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.11.0...v2.12.0)

### 🚀 Enhancements

- Scroll to top of the table on page change (a848dea)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.11.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.10.1...v2.11.0)

### 🚀 Enhancements

- Add scroll-to-top functionality on page change in Table component (3351e39)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.10.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.10.0...v2.10.1)

### 💅 Refactors

- Replace toRef with computed for state properties in useObjectLoader and update type definitions in apiObjectHelper (46ef19e)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.10.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.9.0...v2.10.0)

### 🚀 Enhancements

- Add custom scrollbar styles for better UI experience (18f0880)

### 💅 Refactors

- Move dp-font-family variable to the end and add primary disabled color (f9ce67a)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.9.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.8.4...v2.9.0)

### 🚀 Enhancements

- Add clear icon support to DateTime and DateTimeRange components (be0514a)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.8.4

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.8.3...v2.8.4)

### 🩹 Fixes

- Remove default type for generics in IUseTable and IUseTableSimple interfaces (4a6b556)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.8.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.8.2...v2.8.3)

### 🩹 Fixes

- Correct theme configuration key in InputSelect component (91e0b59)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.8.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.8.1...v2.8.2)

### 🩹 Fixes

- Update type handling in InputDateTimeRange component and add meta field to TableColumn type (98d43a0)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.8.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.8.0...v2.8.1)

### 🩹 Fixes

- Correct icon prop usage and update theme slot naming in Empty component (44d5053)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.8.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.7...v2.8.0)

### 🚀 Enhancements

- Add date range input component and update form field mappings (dc1865c)
- Add custom font family variable to CSS root (5357832)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.7

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.6...v2.7.7)

### 🩹 Fixes

- Update @pinia/nuxt version to ^0.11.0 and adjust loaderObject to use reactive state (a604e70)

### 🏡 Chore

- **release:** V2.7.6 (bd6fab4)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.6

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.6...v2.7.6)

### 🩹 Fixes

- Update @pinia/nuxt version to ^0.11.0 and adjust loaderObject to use reactive state (a604e70)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.6

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.5...v2.7.6)

### 🩹 Fixes

- Update @pinia/nuxt and pinia versions for compatibility (a2bb2a8)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.5

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.4...v2.7.5)

### 💅 Refactors

- Simplify status initialization using ObjectHelper.createStatus (4b78507)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.4

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.3...v2.7.4)

### 🩹 Fixes

- Initialize status with explicit properties for better clarity (1454037)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.2...v2.7.3)

### 🏡 Chore

- Update @pinia/nuxt dependency to use caret versioning and add additional packages to Vite optimizeDeps (b9a1e36)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.1...v2.7.2)

### 🩹 Fixes

- **deps:** Update @pinia/nuxt to version 0.11.0 (bf388da)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.7.0...v2.7.1)

### 💅 Refactors

- Replace reactive with refs for state management in useObjectLoader and apiObjectHelper (2a41f99)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.7.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.6.2...v2.7.0)

### 🚀 Enhancements

- Enhance upload dropzone with improved styling and configuration options (af35cdc)

### 🩹 Fixes

- **deps:** Update @pinia/nuxt to version 0.7.0 and adjust eslint ignore pattern (f4f5b5c)

### 💅 Refactors

- Format code for consistency and readability (8bf6c54)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.6.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.6.1...v2.6.2)

### 🩹 Fixes

- **deps:** Update @nuxt/ui to version 3.1.3 and related dependencies (26732ef)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.6.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.6.0...v2.6.1)

### 🩹 Fixes

- **module:** Enhance build and vite options for improved dependency management (3d99c47)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.6.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.5.5...v2.6.0)

### 🚀 Enhancements

- **module:** Add priority option to addComponentsDir function (0a18bfb)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.5.5

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.5.4...v2.5.5)

### 🩹 Fixes

- **fields:** Add password type binding and map INPUT_TYPES.PASSWORD to FormInputText (f374bc2)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.5.4

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.5.3...v2.5.4)

### 💅 Refactors

- **config:** Replace useAppConfig with direct appConfig import for core and UI configurations (114731a)

### ❤️ Contributors

- Passakon Puttasuwan <<EMAIL>>

## v2.5.3

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.5.2...v2.5.3)

### 🩹 Fixes

- **dialog:** Update color and icon retrieval to use dialogConfig (ca05709)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.5.2

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.5.1...v2.5.2)

### 💅 Refactors

- **dialog:** Use dialogConfig for icon retrieval in Dialog component (f14c665)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.5.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.5.0...v2.5.1)

### 🩹 Fixes

- **config:** Replace direct app config import with useAppConfig for consistency (39db989)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.5.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.4.1...v2.5.0)

### 🚀 Enhancements

- **theme:** Add icons theme and update Loader component icon prop (1211cd4)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.4.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.4.0...v2.4.1)

### 🩹 Fixes

- **dialog:** Import DialogType for improved type safety (f7d505d)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.4.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.3.0...v2.4.0)

### 🚀 Enhancements

- **dialog:** Enhance dialog component with confirm functionality and improved styling (a2623be)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.3.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.2.0...v2.3.0)

### 🚀 Enhancements

- **dialog:** Enhance props handling with computed propsSafe for improved safety and flexibility (678d341)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))

## v2.2.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.1.1...v2.2.0)

### 🚀 Enhancements

- **lodash:** Migrate _get import to local lodash utility and enhance type safety (73a9b13)

### 💅 Refactors

- Replace lodash get import with custom import from #imports (0157be0)

### ❤️ Contributors

- PskCluB ([@pskclub](http://github.com/pskclub))
- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.1.1

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/v2.1.0...v2.1.1)

### 💅 Refactors

- Replace custom _get import with lodash's get in helper files (c30e2bf)
- Update CSS path to use runtime directory for main.css (7fcb35d)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))

## v2.1.0

[compare changes](https://gitlab.finema.co/finema/ui-kit/compare/1.4.216...v2.1.0)

### 🚀 Enhancements

- Lock (3fe8bb3)
- Lock (f79ff65)
- Lint (e3dea2e)
- Lint (5638e0d)
- Lint (4e106d3)
- Lint (d21fdc3)
- Lint (d1f2056)
- Lint (8afd713)
- Lint (2a9baad)
- Lint (8736a60)
- Lint (1888265)
- Lint (49e9636)
- Lint (0e724c0)
- Module (6d7c79f)
- Module (5636d30)
- Refactor file structure and update button colors (6b2f535)
- Remove @vee-validate/zod dependency from package.json (717c07b)
- Add loader component and update UI configuration (502be6a)
- Update Loader component to use computed UI configuration and improve structure (01bb05a)
- Implement dialog component with customizable themes and improved structure (142304e)
- Swap confirm and cancel button classes and enhance dialog props with icon support (81d0232)
- Update notification handling by adding error notification and refactoring useNotification to utilize Toast (843510e)
- Add textarea input component with customizable properties and integrate into form (211526c)
- Add mock API endpoints and update table components (30b5e14)
- Add mock API endpoints and update table components (11b63d6)
- Add mock API endpoints and update table components (b4afbd7)
- Add mock API endpoints and update table components (e714dfb)
- **form:** Add new input field components (toggle, checkbox, select, multi-select)` (a31a66d)
- **form:** Add new input field components (toggle, checkbox, select, multi-select)` (eb99631)
- **form:** Add new input field components (toggle, checkbox, select, multi-select)` (34f20db)
- Add URL parameter binding support via bindParamsToUrl` (0c42b41)
- **form:** Enhance form validation and add dynamic select options (aeec176)
- **form:** Implement dynamic component mapping and enhance field binding logic (166d310)
- **image:** Add Image component with loading and error slots (4f01154)
- **devtools:** Implement draggable and resizable debug tools panel in App.vue (ac0291a)
- **devtools:** Refactor DevTools implementation by replacing inline code with DevToolsWindow component (a31bf92)
- **devtools:** Refactor DevTools implementation by replacing inline code with DevToolsWindow component (0f67f34)
- **ui:** Enhance theme integration by updating UI class bindings across components (7900a4b)
- **flexdeck:** Update event handlers for page change and search methods (3abec6d)
- **ui:** Integrate core configuration for theme and loading indicator across components (5833374)
- **ui:** Add navigation for cookbook section and update sidebar layout (5366fc4)
- **package:** Update module exports and add types support for .mts files (cc6563e)
- **form:** Add number input component and update form validation schema (69bdbbc)
- **form:** Add number input component and update form validation schema (774fa0f)
- **form:** Add number input component and update form validation schema (f52acc0)
- **form:** Enhance validation schema for number input and update description handling (c2bca94)
- **config:** Update useCoreConfig to return full Core type instead of partial (36271c2)
- **form:** Add search event to select component and update types (a9c26a9)
- **form:** Add avatar support for role options in select component (cd3d9b8)
- **form:** Update label-key to use 'label' for improved option display (c3f8a0c)
- **module:** Update Nuxt kit version and enhance theme color structure (9c95a5c)
- **form:** Add DateTime field component and integrate with form handling (489da80)
- **form:** Add DateTime field component and integrate with form handling (a96ded5)

### 🩹 Fixes

- **deps:** Pin nuxt version to 3.17.3 in package.json and bun.lock (82c2d8a)

### 💅 Refactors

- Simplify and reorganize page loader structure` (d24ff77)
- Update generic type parameters for object loader interfaces and improve item update logic (530752e)
- **fields:** Simplify component rendering logic in Fields.vue (9d0017b)
- **table:** Enhance column handling and improve pagination logic in Base.vue and Simple.vue (f6133d5)
- **loader:** Update loader interfaces to use ComputedRef for state management (93b9201)
- **table:** Enhance type safety by updating generic parameters in useTable and useTableSimple (9f563a9)
- **flexdeck:** Update loading and fetch methods for improved data handling (8ac3255)
- **form:** Clean up data items in Log component and improve formatting in useFieldHOC (dcd6cc3)
- **ui:** Replace U* components with their corresponding base components (9c290c3)

### 📖 Documentation

- Update README to reflect new UI kit branding and features (60567eb)

### 🏡 Chore

- Update dependencies and add eslint cache to .gitignore (662135d)
- Remove bun.lockb file (63f53ef)

### ❤️ Contributors

- Passakon Puttasuwan ([@pskclub](http://github.com/pskclub))
- PskCluB ([@pskclub](http://github.com/pskclub))

