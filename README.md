<!--
Get your module up and running quickly.

Find and replace all on all files (CMD+SHIFT+F):
- Name: Finema UI Kit (Note: package.json has name: @finema/core, consider aligning these)
- Package name: @finema/core
- Description: A comprehensive UI kit for Finema projects.
-->

# Finema UI Kit

[![npm version][npm-version-src]][npm-version-href]
[![npm downloads][npm-downloads-src]][npm-downloads-href]
[![License][license-src]][license-href]
[![Nuxt][nuxt-src]][nuxt-href]

A comprehensive UI kit for building consistent and beautiful user interfaces for Finema projects, designed to integrate seamlessly with Nuxt. This package is named `@finema/core` on npm.

- [✨ &nbsp;Release Notes](/CHANGELOG.md)
- [📖 &nbsp;Storybook](https://your-storybook-url.com) <!-- Add a link to your Storybook -->
- [🕹️ &nbsp;Playground](#playground)

## Features

<!-- Highlight some of the features your UI kit provides here -->
- 🎨 &nbsp;Wide range of customizable components, built for Vue and Nuxt
- 📱 &nbsp;Responsive design out-of-the-box
- ♿ &nbsp;Accessibility focused
- 🛠️ &nbsp;Easy to integrate and use with Nuxt auto-import capabilities

## 🚀 Installation

Install the UI kit in your project using npm or yarn:

```bash
npm install @finema/core
```

or

```bash
yarn add @finema/core
```

## 🛠️ Available Scripts

In the project directory, you can run the following scripts:

- `npm run dev` or `yarn dev`: Runs the playground app in development mode.
- `npm run dev:build` or `yarn dev:build`: Builds the playground app.
- `npm run dev:prepare` or `yarn dev:prepare`: Prepares the development environment.
- `npm run lint` or `yarn lint`: Lints the codebase.
- `npm run test` or `yarn test`: Runs tests.
- `npm run release` or `yarn release`: Creates a new release (lints, tests, builds, publishes).

## 🕹️ Playground

This project includes a `playground` directory that you can use to test and experiment with the UI components.

To run the playground:
1. Navigate to the `playground` directory.
2. Install dependencies if you haven't already (`npm install` or `yarn install`).
3. Run `npm run dev` or `yarn dev`.

## 🤝 Contributing

Contributions are welcome! Please refer to the `CONTRIBUTING.md` file for guidelines. (You'll need to create this file).

<!--
Badge URLs - Keep these at the bottom of the file for better readability
-->
[npm-version-src]: https://img.shields.io/npm/v/@finema/core.svg
[npm-version-href]: https://npmjs.com/package/@finema/core
[npm-downloads-src]: https://img.shields.io/npm/dm/@finema/core.svg
[npm-downloads-href]: https://npmjs.com/package/@finema/core
[license-src]: https://img.shields.io/npm/l/@finema/core.svg
[license-href]: https://npmjs.com/package/@finema/core
[nuxt-src]: https://img.shields.io/badge/Nuxt-00DC82?logo=nuxt.js
[nuxt-href]: https://nuxt.com
