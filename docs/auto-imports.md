# Auto-Import Configuration for Finema UI Kit

This document explains how all interfaces and types are automatically imported in the Finema UI Kit Nuxt module.

## Overview

The Finema UI Kit module is configured to automatically import all TypeScript interfaces and types, eliminating the need for manual import statements when using the library in your Nuxt applications.

## What's Auto-Imported

### Core Types
- `IError` - Standard error interface
- `IOption` - Option interface for select components
- `IGetParams` - API parameter interface

### API Types
- `IAPIOptions` - Base API options interface
- `IPageOptions` - Pagination options interface
- `IStatus` - Loading/error status interface
- `IAPIFetchState<T>` - API fetch state interface
- `IAPIObjectState<T>` - API object state interface
- `IAPIListState<T>` - API list state interface
- And many more API-related interfaces...

### Form Types
- `INPUT_TYPES` - Enum for all input types
- `<PERSON>ormField` - Union type for all form fields
- `IFieldProps` - Base field properties interface
- `ITextField` - Text input field interface
- `INumberField` - Number input field interface
- `ISelectField` - Select field interface
- `ICheckboxField` - Checkbox field interface
- `IRadioField` - Radio field interface
- `IDateTimeField` - DateTime field interface
- `IUploadDropzoneAutoField` - Upload field interface
- `IWYSIWYGField` - WYSIWYG editor field interface
- And all other form-related types...

### Table Types
- `COLUMN_TYPES` - Enum for all column types
- `TableColumn<T>` - Table column interface
- `ITableOptions<T>` - Table configuration interface
- `ISimpleTableOptions<T>` - Simple table options interface

### Validation Functions
- `useForm` - Main form composition function from vee-validate
- `useField` - Field composition function from vee-validate
- `useFormErrors` - Form errors composition function from vee-validate
- `useFormValues` - Form values composition function from vee-validate
- `useIsFormDirty` - Form dirty state composition function from vee-validate
- `useIsFormValid` - Form validation state composition function from vee-validate
- `useResetForm` - Form reset composition function from vee-validate
- `useSubmitForm` - Form submit composition function from vee-validate
- `toTypedSchema` - Schema converter for vee-validate + valibot integration
- `v` - Complete valibot namespace (contains object, string, number, email, pipe, optional, array, minLength, maxLength, etc.)

## How It Works

The auto-import system works through the Nuxt module configuration in `src/module.ts`:

```typescript
// Add main types directory
addImportsDir(resolve(runtimeDir, 'types'))

// Add component-specific type files for auto-import
addImportsDir(resolve(runtimeDir, 'components/Form'))
addImportsDir(resolve(runtimeDir, 'components/Table'))
addImportsDir(resolve(runtimeDir, 'components/FlexDeck'))
// ... and all other component directories
```

## Centralized Type Exports

All types are re-exported from `src/runtime/types/index.ts`, which serves as a central hub for all type definitions:

```typescript
// Core types and interfaces
export * from './common'
export * from './lib'

// Form types and interfaces
export * from '../components/Form/types'
export * from '../components/Form/InputText/types'
// ... all other form component types

// Table types and interfaces
export * from '../components/Table/types'

// Re-export enum types for better accessibility
export { INPUT_TYPES } from '../components/Form/types'
export { COLUMN_TYPES } from '../components/Table/types'
```

## Usage Examples

### Using Form Types (No Imports Needed!)

```vue
<script setup lang="ts">
// All types are automatically available!
const textField: ITextField = {
  type: INPUT_TYPES.TEXT,
  props: {
    name: 'username',
    label: 'Username',
    required: true,
  },
}

const selectField: ISelectField = {
  type: INPUT_TYPES.SELECT,
  props: {
    name: 'category',
    label: 'Category',
    options: [
      { value: '1', label: 'Option 1' },
      { value: '2', label: 'Option 2' },
    ],
  },
}
</script>
```

### Using Table Types (No Imports Needed!)

```vue
<script setup lang="ts">
// Table types are automatically available!
const columns: TableColumn[] = [
  {
    accessorKey: 'name',
    type: COLUMN_TYPES.TEXT,
    header: 'Name',
  },
  {
    accessorKey: 'email',
    type: COLUMN_TYPES.TEXT,
    header: 'Email',
  },
  {
    accessorKey: 'createdAt',
    type: COLUMN_TYPES.DATE_TIME,
    header: 'Created',
  },
]

const tableOptions: ITableOptions<any> = {
  rawData: [],
  status: { isLoading: false, isError: false, isSuccess: true, isLoaded: true, errorData: null },
  columns,
  pageOptions: {
    currentPage: 1,
    totalPage: 1,
    currentPageCount: 0,
    totalCount: 0,
    limit: 10,
  },
  isPreventRouteChange: false,
}
</script>
```

### Using API Types (No Imports Needed!)

```vue
<script setup lang="ts">
// API types are automatically available!
const apiOptions: IAPIOptions = {
  _status: 200,
  _timestamp: Date.now(),
}

const pageOptions: IPageOptions = {
  currentPage: 1,
  totalPage: 10,
  currentPageCount: 20,
  totalCount: 200,
  limit: 20,
}

const status: IStatus = {
  isLoading: false,
  isError: false,
  isSuccess: true,
  isLoaded: true,
  errorData: null,
}
</script>
```

### Using Validation Functions (No Imports Needed!)

```vue
<script setup lang="ts">
// All validation functions are automatically available!

// Create validation schema using auto-imported valibot functions
const validationSchema = toTypedSchema(
  v.object({
    username: v.pipe(v.string(), v.minLength(3)),
    email: v.pipe(v.string(), v.email()),
    age: v.optional(v.number()),
    preferences: v.array(v.string()),
  })
)

// Use auto-imported vee-validate composables
const { handleSubmit, errors, meta, values } = useForm({
  validationSchema,
})

const { value: username } = useField('username')
const { value: email } = useField('email')

const onSubmit = handleSubmit((formValues) => {
  console.log('Form submitted with:', formValues)
})

// Check form state using auto-imported composables
const formErrors = useFormErrors()
const isFormValid = useIsFormValid()
const isFormDirty = useIsFormDirty()
</script>
```

## Benefits

1. **No Import Statements**: Write cleaner code without managing imports
2. **Better Developer Experience**: IDE autocomplete works seamlessly
3. **Type Safety**: Full TypeScript support with all interfaces
4. **Consistency**: All types follow the same naming conventions
5. **Maintainability**: Centralized type management

## Testing Auto-Imports

Visit the `/types-demo` page in the playground to see live examples of all auto-imported types in action. The demo page shows how to use various types without any import statements.

## Adding New Types

When adding new types to the library:

1. Create your type definitions in the appropriate component's `types.ts` file
2. Add the export to `src/runtime/types/index.ts`
3. Ensure the directory is included in the `addImportsDir` calls in `src/module.ts`

The auto-import system will automatically pick up your new types!
