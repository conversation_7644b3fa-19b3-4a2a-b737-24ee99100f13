export default defineAppConfig({

  ui: {
    card: {
      slots: {
        header: 'text-lg font-bold',
      },
    },
    switch: {
      slots: {
        base: 'cursor-pointer',
        label: 'cursor-pointer',
      },
    },
    breadcrumb: {
      variants: {
        active: {
          true: {
            link: 'text-black font-normal',
          },
        },
      },
    },
    tabs: {
      slots: {
        trigger: 'data-[state=active]:font-bold cursor-pointer',
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    modal: {
      slots: {
        title: 'text-xl font-bold',
        content: 'divide-none',
        body: 'sm:pt-0 pt-0',
      },
    },
    pagination: {
      slots: {
        first: 'disabled:hidden',
        prev: 'disabled:hidden',
        next: 'disabled:hidden',
        last: 'disabled:hidden',
      },
    },
    table: {
      slots: {
        root: 'rounded-t-md rounded-b-md bg-white',
        captionContainer: 'hidden',
        paginationInfo: 'text-gray-600',
        paginationContainer: 'items-center',
        thead: 'bg-[#475569]',
        th: 'text-white whitespace-nowrap',
        td: 'text-[#222222]',
      },
    },
    formField: {
      slots: {
        label: 'font-bold',
      },
    },
    input: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    dateTime: {
      slots: {
        clearIcon: 'size-6 mr-3',
      },
    },
    selectMenu: {
      slots: {
        base: 'cursor-pointer w-full',
        item: 'cursor-pointer',
        clearIcon: 'size-6',
      },
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    textarea: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    button: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 font-bold',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
  },
  core: {
    site_name: 'UIKIT Playground',
    is_thai_year: true,
  },
})
