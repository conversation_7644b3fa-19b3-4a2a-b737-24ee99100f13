<template>
  <div class="flex h-full w-full">
    <!-- Mobile Sidebar -->
    <Slideover
      v-model:open="isMobileMenuOpen"
      side="left"
      class="w-80"
      :close="{
        color: 'primary',
        variant: 'outline',
        class: 'rounded-full',
      }"
    >
      <template #content>
        <div class="flex h-full flex-col gap-y-5 bg-gray-900 px-6 pb-4">
          <div class="flex h-16 shrink-0 items-center">
            <img
              class="h-8 w-auto"
              src="/images/logo.png"
              alt="Company Logo"
            />
          </div>
          <SidebarContent />
        </div>
      </template>
    </Slideover>

    <!-- Desktop Sidebar -->
    <div
      class="
        hidden
        lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col
      "
    >
      <div
        class="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4"
      >
        <div class="flex h-16 shrink-0 items-center">
          <img
            class="h-8 w-auto"
            src="/images/logo.png"
            alt="Company Logo"
          />
        </div>
        <SidebarContent />
      </div>
    </div>

    <!-- Main Content Area -->
    <div
      class="
        w-full
        lg:pl-[288px]
      "
    >
      <!-- Top Navigation Bar -->
      <div
        class="
          sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b
          border-gray-200 bg-white px-4 shadow-sm
          sm:gap-x-6 sm:px-6
          lg:hidden lg:px-8
        "
      >
        <!-- Mobile Menu Button -->
        <Button
          variant="ghost"
          square
          class="
            -m-2.5 p-2.5 text-gray-700
            lg:hidden
          "
          @click="isMobileMenuOpen = true"
        >
          <Icon
            name="i-heroicons-bars-3"
            class="size-6"
          />
        </Button>

        <!-- Mobile Separator -->
        <div
          class="
            h-6 w-px bg-gray-900/10
            lg:hidden
          "
          aria-hidden="true"
        />
      </div>

      <!-- Main Content -->
      <main class="py-10">
        <div
          class="
            px-4
            sm:px-6
            lg:px-8
          "
        >
          <slot />
        </div>
      </main>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import SidebarContent from './components/SidebarContent.vue'

// Mobile menu state
const isMobileMenuOpen = ref(false)
</script>
