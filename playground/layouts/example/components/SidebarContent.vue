<template>
  <nav class="flex flex-1 flex-col">
    <ul
      role="list"
      class="flex flex-1 flex-col gap-y-2"
    >
      <!-- Main Navigation -->
      <li>
        <ul
          role="list"
          class="-mx-2 space-y-1"
        >
          <li
            v-for="item in navigationItems"
            :key="item.label"
          >
            <NuxtLink
              :to="item.to"
              :class="[
                'group flex gap-x-3 rounded-md p-2 text-sm font-semibold',
                isCurrentRoute(item.to)
                  ? 'bg-gray-800 text-white'
                  : `
                    text-gray-400
                    hover:bg-gray-800 hover:text-white
                  `,
              ]"
            >
              <Icon
                :name="item.icon"
                class="size-6 shrink-0"
              />
              {{ item.label }}
            </NuxtLink>
          </li>
        </ul>
      </li>
      <hr />
      <p class="mt-2 mb-0 font-bold">
        Cook book
      </p>
      <!-- Main Navigation -->
      <li>
        <ul
          role="list"
          class="-mx-2 space-y-1"
        >
          <li
            v-for="item in navigationCookBookItems"
            :key="item.label"
          >
            <NuxtLink
              :to="item.to"
              :class="[
                'group flex gap-x-3 rounded-md p-2 text-sm font-semibold',
                isCurrentRoute(item.to)
                  ? 'bg-gray-800 text-white'
                  : `
                    text-gray-400
                    hover:bg-gray-800 hover:text-white
                  `,
              ]"
            >
              <Icon
                :name="item.icon"
                class="size-6 shrink-0"
              />
              {{ item.label }}
            </NuxtLink>
          </li>
        </ul>
      </li>
    </ul>
  </nav>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router'

const route = useRoute()

const navigationItems = [
  {
    label: 'Home',
    to: '/',
    icon: 'i-heroicons-home',
  },
  {
    label: 'Dialog',
    to: '/dialog',
    icon: 'i-heroicons-chat-bubble-bottom-center',
  },
  {
    label: 'Flexdeck',
    to: '/flexdeck',
    icon: 'i-heroicons-view-columns',
  },
  {
    label: 'Form',
    to: '/form',
    icon: 'i-heroicons-document-text',
  },
  {
    label: 'Notification',
    to: '/notification',
    icon: 'i-heroicons-bell-alert',
  },
  {
    label: 'Table',
    to: '/table',
    icon: 'i-heroicons-table-cells',
  },
  {
    label: 'Image',
    to: '/image',
    icon: 'i-heroicons-photo',
  },
]

const navigationCookBookItems = [
  {
    label: 'Register page',
    to: '/cookbook/register',
    icon: 'i-heroicons-user-plus',
  },
]

const isCurrentRoute = (path: string) => {
  return route.path === path
}
</script>
