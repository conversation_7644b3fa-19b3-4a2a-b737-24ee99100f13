<template>
  <div class="space-y-8">
    <!-- Introduction -->
    <div>
      <h1 class="text-2xl font-bold">
        Register Form
      </h1>
      <p class="mt-2 text-gray-600">
        Create your account with validation.
      </p>
    </div>

    <div
      class="
        mx-auto w-full
        md:max-w-[671px]
      "
    >
      <!-- Debug Tools -->
      <div class="mb-4">
        <Log
          title="Form Values"
          :data-items="[form.values]"
        />
      </div>

      <!-- Form -->
      <Form
        class="mt-8 w-full"
        @submit="onSubmit"
      >
        <FormFields
          :options="fields"
        />

        <div class="space-x-4">
          <Button
            type="submit"
            class="mt-6"
          >
            Register
          </Button>
        </div>
      </Form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { toTypedSchema } from '@vee-validate/valibot'
import { useForm } from 'vee-validate'
import { INPUT_TYPES } from '#core/components/Form/types'
import { createFormFields } from '#core/composables/useForm'

definePageMeta({
  layout: 'example',
})

useSeoMeta({
  title: 'Register Form',
})

const registerSchema = v.pipe(v.object({
  username: v.pipe(v.string(), v.nonEmpty()),
  password: v.pipe(v.string(), v.nonEmpty(), v.minLength(6, 'Password must be at least 6 characters long.')),
  confirmPassword: v.pipe(v.string(), v.nonEmpty(), v.minLength(1, 'Please confirm your password.')),
}), v.forward(
  v.check(
    (input) => input.password === input.confirmPassword,
    'The two passwords do not match.',
  ),
  ['confirmPassword'],
))

const form = useForm({
  validationSchema: toTypedSchema(registerSchema),
  initialValues: {
    username: '',
    password: '',
    confirmPassword: '',
  },
})

const fields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Username',
      name: 'username',
      placeholder: 'Enter your username',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Password',
      name: 'password',
      type: 'password',
      placeholder: 'Enter your password',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Confirm Password',
      name: 'confirmPassword',
      type: 'password',
      placeholder: 'Confirm your password',
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  console.log('Register Form Submitted:', values)
  alert(JSON.stringify(values, null, 2))
})
</script>
