<template>
  <div class="space-y-12">
    <!-- Introduction -->
    <div>
      <h1 class="text-2xl font-bold">
        FlexDeck Component
      </h1>
      <p class="mt-2 text-gray-600">
        Display items in a grid layout with pagination and infinite scroll options
      </p>
    </div>

    <!-- Standard FlexDeck -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Grid Layout with Pagination
      </h2>
      <p class="text-gray-600">
        Display items in a responsive grid with standard pagination
      </p>

      <FlexDeck
        :options="tableOptions"
        container-class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"
        @pageChange="tableStore.fetchPageChange"
        @search="tableStore.fetchSearch"
      >
        <!-- Loading State -->
        <template #loading-state>
          <div class="flex h-60 items-center justify-center">
            <Icon
              name="i-svg-spinners:180-ring-with-bg"
              class="text-primary size-8"
            />
          </div>
        </template>

        <!-- Empty State -->
        <template #empty-state>
          <div class="min-h-60">
            <p class="text-center text-sm italic">
              No items found
            </p>
          </div>
        </template>

        <!-- Item Template -->
        <template #default="{ row }: { row: ITableItem }">
          <div
            class="
              rounded-xl border bg-white p-3 shadow transition
              hover:shadow-md
            "
          >
            <img
              :src="row.pic"
              :alt="row.name"
              class="mb-3 w-full rounded"
            />
            <h3 class="font-medium">
              {{ row.name }}
            </h3>
            <p class="truncate text-sm text-gray-600">
              {{ StringHelper.truncate(row.email, 50) }}
            </p>
          </div>
        </template>
      </FlexDeck>
    </div>

    <!-- Infinite Scroll FlexDeck -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Infinite Scroll Layout
      </h2>
      <p class="text-gray-600">
        Display items in a single column with infinite scroll loading
      </p>

      <FlexDeck
        :options="tableInfiniteOptions"
        container-class="grid grid-cols-1 gap-4 max-w-2xl"
        @pageChange="flexDeckStore.fetchPageChange"
        @search="flexDeckStore.fetchSearch"
      >
        <!-- Loading State -->
        <template #loading-state>
          <div class="flex h-60 items-center justify-center">
            <Icon
              name="i-svg-spinners:180-ring-with-bg"
              class="text-primary size-8"
            />
          </div>
        </template>

        <!-- Empty State -->
        <template #empty-state>
          <div class="min-h-60">
            <p class="text-center text-sm italic">
              No items found
            </p>
          </div>
        </template>

        <!-- Item Template -->
        <template #default="{ row }: { row: ITableItem }">
          <div
            class="
              rounded-xl border bg-white p-4 shadow transition
              hover:shadow-md
            "
          >
            <div class="flex gap-4">
              <img
                :src="row.pic"
                :alt="row.name"
                class="size-24 rounded object-cover"
              />
              <div>
                <h3 class="font-medium">
                  {{ row.name }}
                </h3>
                <p class="mt-1 text-sm text-gray-600">
                  {{ StringHelper.truncate(row.email, 50) }}
                </p>
              </div>
            </div>
          </div>
        </template>
      </FlexDeck>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, StringHelper, useFlexDeck } from '#imports'
import { type ITableItem, useTableStore, useFlexDeckStore } from '~/loaders/useTableLoader'

definePageMeta({
  layout: 'example',
})

useSeoMeta({
  title: 'FlexDeck',
  description: 'Display items in a grid layout with pagination and infinite scroll options',
})

// Initialize stores
const tableStore = useTableStore()
const flexDeckStore = useFlexDeckStore()

// Initial loading states
tableStore.fetchSetLoading()
flexDeckStore.fetchSetLoading()

// Fetch data on mount
onMounted(() => {
  tableStore.fetchPage()
  flexDeckStore.fetchPage()
})

// Standard FlexDeck configuration
const tableOptions = useFlexDeck<ITableItem>({
  repo: tableStore,
  options: {
    isEnabledSearch: true,
  },
})

// Infinite scroll FlexDeck configuration
const tableInfiniteOptions = useFlexDeck<ITableItem>({
  repo: flexDeckStore,
  options: {
    isEnableInfiniteScroll: true,
  },
})
</script>
