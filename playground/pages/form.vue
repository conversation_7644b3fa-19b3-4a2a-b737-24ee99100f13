<template>
  <div class="space-y-8">
    <!-- Introduction -->
    <div>
      <h1 class="text-2xl font-bold">
        Form Component
      </h1>
      <p class="mt-2 text-gray-600">
        A powerful form builder with validation and various input types
      </p>
    </div>

    <div
      class="
        mx-auto w-full
        md:max-w-[671px]
      "
    >
      <!-- Debug Tools -->
      <div class="mb-4">
        <Log
          title="Form Values"
          :data-items="[form.values, post.status, post.data, post.options]"
        />
      </div>

      <!-- Form -->
      <Form
        class="mt-8 w-full"
        @submit="onSubmit"
      >
        <h2 class="mt-6 mb-3 border-b pb-2 text-xl font-semibold">
          Basic Inputs
        </h2>
        <FormFields :options="basicInputFields" />

        <h2 class="mt-8 mb-3 border-b pb-2 text-xl font-semibold">
          Selection Inputs
        </h2>
        <FormFields :options="selectionInputFields" />

        <h2 class="mt-8 mb-3 border-b pb-2 text-xl font-semibold">
          Date & Time Inputs
        </h2>
        <FormFields :options="dateTimeInputFields" />

        <h2 class="mt-8 mb-3 border-b pb-2 text-xl font-semibold">
          Advanced Inputs
        </h2>
        <FormFields :options="advancedInputFields" />

        <div class="space-x-4">
          <Button
            type="submit"
            class="mt-6"
          >
            Submit
          </Button>
          <Button
            color="error"
            variant="outline"
            class="mt-6"
          >
            Button
          </Button>
        </div>
      </Form>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'example',
})

useSeoMeta({
  title: 'form',
})

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.optional(v.string()),
      content: v.optional(v.string()),
      number: v.pipe(
        v.unknown(),
        v.transform((v) => Number(v)),
      ),
      mask: v.nullish(
        v.pipe(
          v.string(),
          v.regex(
            /^\d{3}-\d{3}-\d{4}$/,
            'Invalid phone number format. Expected: ###-###-####',
          ),
        ),
      ),
      desc: v.nullish(
        v.pipe(v.string(), v.maxLength(5, 'Description is too long')),
      ),
      datetime: v.nullish(v.union([v.date(), v.string()])),
      daterange: v.nullish(
        v.object({
          start: v.union([v.date(), v.string()]),
          end: v.union([v.date(), v.string()]),
        }),
      ),
      fileUploadAuto: v.nullish(
        v.object({
          url: v.string(),
          name: v.string(),
          path: v.string(),
          size: v.number(),
        }),
      ),
      fileUpload: v.file(),
    }),
  ),
  initialValues: {
    name: 'John Doe',
    number: 42,
    datetime: '2025-01-15', // example datetime
    daterange: {
      start: '2025-01-01', // example start date
      end: '2025-01-31', // example end date
    },
    content: '<p>test jaa</p>',
  },
})

const post = usePostCreate()

const selectOptions = [
  ObjectHelper.createOption('status_active', 'Active'),
  {
    ...ObjectHelper.createOption('status_inactive', 'Inactive'),
    icon: 'i-lucide-circle-arrow-up',
  },
  {
    type: 'separator' as const,
  },
  {
    ...ObjectHelper.createOption('role_admin', 'Administrator'),
    avatar: {
      src: 'https://github.com/sandros94.png',
      alt: 'sandros94',
    },
  },
  ObjectHelper.createOption('role_editor', 'Editor'),
  ObjectHelper.createOption('role_viewer', 'Viewer'),
  ObjectHelper.createOption('priority_high', 'High Priority'),
  ObjectHelper.createOption('priority_medium', 'Medium Priority'),
  ObjectHelper.createOption('priority_low', 'Low Priority'),
  ObjectHelper.createOption('category_tech', 'Technology'),
  ObjectHelper.createOption('category_finance', 'Finance'),
  ObjectHelper.createOption('category_health', 'Healthcare'),
  ObjectHelper.createOption('country_us', 'United States'),
  ObjectHelper.createOption('country_ca', 'Canada'),
  ObjectHelper.createOption('country_gb', 'United Kingdom'),
  ObjectHelper.createOption('country_au', 'Australia'),
  ObjectHelper.createOption('country_jp', 'Japan'),
  ObjectHelper.createOption('country_de', 'Germany'),
]

// --- Grouped Form Fields ---

// Basic Inputs
const basicInputFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'name',
      name: 'name',
      help: 'help me',
      description: 'description',
      suggestions: [
        'John Doe',
        'Jane Smith',
        'Alice Johnson',
        'Bob Brown',
        'Charlie Davis',
        'Eve White',
        'Frank Black BlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlackBlack',
        'หมาหลง',
      ],
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'number',
      name: 'number',
      min: 0,
      max: 1000,
    },
  },
  {
    type: INPUT_TYPES.TEXTAREA,
    props: {
      label: 'desc',
      name: 'desc',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'mask',
      name: 'mask',
      mask: '###-###-####',
      placeholder: '080-568-1438',
    },
  },
])

// Selection Inputs
const selectionInputFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TOGGLE,
    props: {
      label: 'toggle',
      name: 'toggle',
      description: 'toggle',
    },
  },
  {
    type: INPUT_TYPES.CHECKBOX,
    props: {
      label: 'check',
      name: 'check',
      description: 'check',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'select',
      name: 'select',
      description: 'select',
      clearable: true,
      options: selectOptions,
    },
  },
  {
    type: INPUT_TYPES.SELECT_MULTIPLE,
    props: {
      label: 'selectmulti',
      name: 'selectmulti',
      description: 'selectmulti',
      options: selectOptions,
    },
    on: {
      search: (q: string) => {
        console.log('search', q)
      },
    },
  },
  {
    type: INPUT_TYPES.RADIO,
    props: {
      label: 'Theme',
      name: 'theme',
      options: [
        {
          value: 'light',
          label: 'Light',
          description: 'Light theme for daytime use',
        },
        {
          value: 'dark',
          label: 'Dark',
          description: 'Dark theme for nighttime use',
        },
        {
          value: 'system',
          label: 'System',
          description: 'Follow system preference',
        },
      ],
    },
    on: {
      change: (value: string) => {
        console.log('Theme changed:', value)
      },
    },
  },
  {
    type: INPUT_TYPES.RADIO,
    props: {
      label: 'Plan',
      name: 'plan',
      variant: 'card',
      options: [
        {
          value: 'basic',
          label: 'Basic',
          description: 'Perfect for getting started',
        },
        {
          value: 'pro',
          label: 'Pro',
          description: 'Best for professionals',
        },
        {
          value: 'enterprise',
          label: 'Enterprise',
          description: 'For large organizations',
        },
      ],
    },
  },
])

// Date & Time Inputs
const dateTimeInputFields = createFormFields(() => [
  {
    type: INPUT_TYPES.DATE_TIME,
    props: {
      label: 'datetime',
      name: 'datetime',
      description: 'datetime',
    },
  },
  {
    type: INPUT_TYPES.DATE_RANGE,
    props: {
      label: 'daterange',
      name: 'daterange',
      description:
        'daterange with initial values (last 7 days) - dd-MM-yyyy format',
    },
  },
])

// Advanced Inputs
const advancedInputFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      label: 'Search',
      name: 'search',
      placeholder: 'Search for anything...',
      clearable: true,
      debounce: 500,
    },
    on: {
      search: (query: string) => {
        console.log('Search query:', query)
      },
      clear: () => {
        console.log('Search cleared')
      },
      change: (value: string) => {
        console.log('Search value changed:', value)
      },
    },
  },
  {
    type: INPUT_TYPES.WYSIWYG,
    props: {
      label: 'Content',
      name: 'content',
      description: 'Rich text editor for creating formatted content',
      toolbar: {
        bold: true,
        italic: true,
        underline: true,
        strike: false,
        code: false,
        heading: true,
        paragraph: true,
        bulletList: true,
        orderedList: true,
        blockquote: true,
        codeBlock: false,
        horizontalRule: true,
        link: true,
        image: false,
        youtube: false,
        textAlign: true,
        undo: true,
        redo: true,
      },
    },
    on: {
      change: (content: string) => {
        console.log('Content changed:', content)
      },
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      label: 'File Upload (Auto)',
      name: 'fileUploadAuto',
      placeholder: 'doc, docx, pdf (max. 5MB)',
      requestOptions: useRequestOptions().getMock(),
      uploadPathURL: '/upload',
      accept: '.jpg,.png,application/pdf, video/*',
      maxSize: 5120, // 5MB in KB
    },
    on: {
      change: (file: File | undefined) => {
        console.log('File changed:', file)
      },
      success: (response: any) => {
        console.log('Upload successful:', response)
      },
      delete: () => {
        console.log('File deleted')
      },
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE,
    props: {
      label: 'File Upload',
      name: 'fileUpload',
      placeholder: 'doc, docx, pdf (max. 5MB)',
      accept: '.jpg,.png,application/pdf, video/*',
      maxSize: 5120, // 5MB in KB
    },
    on: {
      change: (file: File | undefined) => {
        console.log('File changed:', file)
      },
      delete: () => {
        console.log('File deleted')
      },
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  post.run()
  console.log(values)
  alert(JSON.stringify(values, null, 2))
}, moveToError)
</script>
