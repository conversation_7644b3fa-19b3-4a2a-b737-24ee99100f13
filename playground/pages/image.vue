<template>
  <div class="space-y-8">
    <!-- Introduction -->
    <div>
      <h1 class="text-2xl font-bold">
        Image Component
      </h1>
      <p class="mt-2 text-gray-600">
        Display images with loading and error states.
      </p>
    </div>

    <!-- Basic Image -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Basic Usage
      </h2>
      <div class="max-w-xs">
        <Image
          :src="img"
          alt="Avatar"
          @click="img = '/images/avatar.png'"
        />
      </div>
    </div>

    <!-- Image with Loading State -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Loading State
      </h2>
      <p>This image will simulate a loading delay.</p>
      <div class="max-w-xs">
        <Image
          src="https://via.placeholder.com/300/CCCCCC/808080?text=Loading..."
          alt="Loading Example"
          class="h-auto w-full"
        />
      </div>
    </div>

    <!-- Image with Error State -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Error State
      </h2>
      <p>This image will intentionally cause an error.</p>
      <div class="max-w-xs">
        <Image
          src="/images/non-existent-image.png"
          alt="Error Example"
          class="h-auto w-full"
        />
      </div>
    </div>

    <!-- Image with Custom Loading Slot -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Custom Loading Slot
      </h2>
      <div class="max-w-xs">
        <Image
          src="https://via.placeholder.com/300/CCCCCC/808080?text=Loading..."
          alt="Custom Loading"
          class="h-auto w-full"
        >
          <template #loading>
            <div
              class="
                flex h-32 w-full items-center justify-center rounded bg-gray-200
              "
            >
              <p class="text-gray-500">
                Custom Loading...
              </p>
            </div>
          </template>
        </Image>
      </div>
    </div>

    <!-- Image with Custom Error Slot -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Custom Error Slot
      </h2>
      <div class="max-w-xs">
        <Image
          src="/images/non-existent-image.png"
          alt="Custom Error"
          class="h-auto w-full"
        >
          <template #error>
            <div
              class="
                flex h-32 w-full flex-col items-center justify-center rounded
                bg-red-100
              "
            >
              <Icon
                name="i-heroicons-face-frown"
                class="mb-2 size-8 text-red-500"
              />
              <p class="text-red-700">
                Oops! Image failed to load.
              </p>
            </div>
          </template>
        </Image>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'example',
})

const img = ref('/images/logo.png')

useSeoMeta({
  title: 'Image Component',
  description: 'Examples of the Image component with loading and error states.',
})
</script>
