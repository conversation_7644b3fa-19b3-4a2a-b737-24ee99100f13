<template>
  <div class="space-y-8">
    <!-- Introduction -->
    <div>
      <h1 class="text-2xl font-bold">
        Notification Component
      </h1>
      <p class="mt-2 text-gray-600">
        Display toast notifications with different styles and behaviors
      </p>
    </div>

    <!-- Basic Notifications -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Notification Types
      </h2>
      <div class="flex max-w-[180px] flex-col space-y-4">
        <!-- Info Notification -->
        <Button
          label="Show Info"
          color="info"
          @click="showNotification('info')"
        />

        <!-- Warning Notification -->
        <Button
          label="Show Warning"
          color="warning"
          @click="showNotification('warning')"
        />

        <!-- Success Notification -->
        <Button
          label="Show Success"
          color="success"
          @click="showNotification('success')"
        />

        <!-- Error Notification -->
        <Button
          label="Show Error"
          color="error"
          @click="showNotification('error')"
        />
      </div>
    </div>

    <!-- Custom Notifications -->
    <div class="space-y-4">
      <h2 class="text-xl font-medium">
        Custom Options
      </h2>
      <div class="flex max-w-[180px] flex-col space-y-4">
        <!-- With Custom Timeout -->
        <Button
          label="Short Timeout"
          color="primary"
          @click="showCustomNotification('info', { duration: 500 })"
        />

        <!-- With Custom Icon -->
        <Button
          label="Custom Icon"
          color="primary"
          @click="showCustomNotification('success', { icon: 'i-heroicons-star' })"
        />

        <!-- With Action -->
        <Button
          label="With Action"
          color="primary"
          @click="showActionNotification"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useNotification } from '#imports'

definePageMeta({
  layout: 'example',
})

useSeoMeta({
  title: 'Notification Component',
  description: 'Display toast notifications with different styles and behaviors',
})

const notification = useNotification()

// Generic notification handler
const showNotification = (type: 'info' | 'warning' | 'success' | 'error') => {
  const titles = {
    info: 'Information',
    warning: 'Warning',
    success: 'Success',
    error: 'Error',
  }

  notification[type]({
    title: titles[type],
    description: `This is a ${type} notification`,
    duration: 5000,
  })
}

// Custom notification handler
const showCustomNotification = (
  type: 'info' | 'warning' | 'success' | 'error',
  options: Record<string, any>,
) => {
  notification[type]({
    title: 'Custom Notification',
    description: 'Notification with custom options',
    ...options,
  })
}

// Action notification example
const showActionNotification = () => {
  notification.info({
    title: 'Action Required',
    description: 'Click the button to perform an action',
    duration: 8000,
    actions: [
      {
        label: 'Action',
        onClick: () => {
          notification.success({
            title: 'Action Completed',
            description: 'You clicked the action button!',
            duration: 3000,
          })
        },
      },
    ],
  })
}
</script>
