<template>
  <div class="container mx-auto p-8">
    <h1 class="mb-8 text-3xl font-bold">
      Auto-imported Types Demo
    </h1>

    <div class="grid gap-6">
      <div class="rounded-lg bg-white p-6 shadow">
        <h2 class="mb-4 text-xl font-semibold">
          Form Types Demo
        </h2>
        <p class="mb-4 text-gray-600">
          All form-related types are auto-imported:
        </p>
        <code class="block rounded bg-gray-100 p-4 text-sm">
          INPUT_TYPES, IFormField, IFieldProps, ITextField, INumberField,
          ISelectField, ICheckboxField, IRadioField, etc.
        </code>
      </div>

      <div class="rounded-lg bg-white p-6 shadow">
        <h2 class="mb-4 text-xl font-semibold">
          Table Types Demo
        </h2>
        <p class="mb-4 text-gray-600">
          All table-related types are auto-imported:
        </p>
        <code class="block rounded bg-gray-100 p-4 text-sm">
          COLUMN_TYPES, TableColumn, ITableOptions, ISimpleTableOptions
        </code>
      </div>

      <div class="rounded-lg bg-white p-6 shadow">
        <h2 class="mb-4 text-xl font-semibold">
          API Types Demo
        </h2>
        <p class="mb-4 text-gray-600">
          All API-related types are auto-imported:
        </p>
        <code class="block rounded bg-gray-100 p-4 text-sm">
          IAPIOptions, IPageOptions, IStatus, IAPIFetchState, IAPIObjectState
        </code>
      </div>

      <div class="rounded-lg bg-white p-6 shadow">
        <h2 class="mb-4 text-xl font-semibold">
          Common Types Demo
        </h2>
        <p class="mb-4 text-gray-600">
          All common types are auto-imported:
        </p>
        <code class="block rounded bg-gray-100 p-4 text-sm">
          IError, IOption, IGetParams
        </code>
      </div>

      <div class="rounded-lg bg-white p-6 shadow">
        <h2 class="mb-4 text-xl font-semibold">
          Validation Functions Demo
        </h2>
        <p class="mb-4 text-gray-600">
          All validation functions are auto-imported:
        </p>
        <code class="block rounded bg-gray-100 p-4 text-sm">
          useForm, useField, toTypedSchema, v (valibot namespace), etc.
        </code>
      </div>

      <div class="rounded-lg bg-white p-6 shadow">
        <h2 class="mb-4 text-xl font-semibold">
          Type Usage Example
        </h2>
        <p class="mb-4 text-gray-600">
          Example showing types in action:
        </p>
        <pre class="overflow-x-auto rounded bg-gray-100 p-4 text-sm">{{ exampleTypes }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// All these types are auto-imported - no import statements needed!

// Example table column configuration using auto-imported types
const sampleTableColumn: TableColumn = {
  accessorKey: 'name',
  type: COLUMN_TYPES.TEXT,
  header: 'Name',
}

// Example API options using auto-imported types
const sampleAPIOptions: IAPIOptions = {
  _status: 200,
  _timestamp: Date.now(),
}

// Example common option using auto-imported types
const sampleOption: IOption = {
  value: 'option1',
  label: 'Option 1',
}

// Example error using auto-imported types
const sampleError: IError = {
  code: 'VALIDATION_ERROR',
  message: 'Field is required',
}

// Display example for template
const exampleTypes = {
  'Table Column': sampleTableColumn,
  'API Options': sampleAPIOptions,
  'Option': sampleOption,
  'Error': sampleError,
}

// Demonstrate that enums are also auto-imported and can be used in type annotations
// INPUT_TYPES and COLUMN_TYPES are available without imports

// Example validation schema using auto-imported functions - no imports needed!
const validationSchema = toTypedSchema(
  v.object({
    username: v.pipe(v.string(), v.minLength(3)),
    email: v.pipe(v.string(), v.email()),
    age: v.optional(v.number()),
  }),
)

// Example form using auto-imported useForm - no imports needed!
const {
  handleSubmit, errors, meta,
} = useForm({
  validationSchema,
})

const onSubmit = handleSubmit((values) => {
  console.log('Form submitted:', values)
})
</script>
