import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '5618d581-7a31-4b1d-be2b-ffcf3308c0f8',
      name: '<PERSON>',
      email: 'johndo<PERSON>@email.commmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm',
      salary: 3123123,
      pic: 'https://placehold.co/600x400',
      data: {
        value: 'test nested',
      },
      createdAt: '2024-02-03T00:00:00Z',
      createdBy: null,
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913be5a3ec7c',
      name: '<PERSON>',
      email: '<EMAIL>',
      salary: 56456,
      pic: 'https://placehold.co/600x400',
      data: {
        value: 'test nested',
      },
      createdAt: '2024-02-03T00:00:00Z',
      createdBy: 'user_uuid',
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913asda2a3ec7c',
      name: 'Ive Got A Name',
      email: '<EMAIL>',
      salary: 43224,
      pic: 'https://placehold.co/600x400',
      data: {
        value: 'test nested',
      },
      createdAt: '2024-02-03T00:00:00Z',
      createdBy: 'user_uuid',
    },
    {
      id: '5618d581-7a31-4b1d-be2b-ffcf3308c0f8',
      name: 'John Xerxes Doe',
      pic: 'https://placehold.co/600x400',
      email: '<EMAIL>',
      createdAt: '2024-02-03T00:00:00Z',
      createdBy: null,
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913be5a3ec7c',
      name: 'Peter Benjamin Parker',
      pic: 'https://placehold.co/600x400',
      email: '<EMAIL>',
      createdAt: '2024-02-03T00:00:00Z',
      createdBy: 'user_uuid',
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-91300222a3ec7c',
      name: 'Ive Got A Name 2',
      pic: 'https://placehold.co/600x400',
      email: '<EMAIL>',
      createdAt: '2024-02-03T00:00:00Z',
      createdBy: 'user_uuid',
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
