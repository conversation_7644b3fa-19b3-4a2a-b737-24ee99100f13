export const nuxtAppOptions = {
  head: {
    htmlAttrs: {
      lang: 'en',
    },
    meta: [
      {
        charset: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      {
        id: 'description',
        name: 'description',
        content: '',
      },
      {
        name: 'format-detection',
        content: 'telephone=no',
      },
    ],
    link: [
      {
        rel: 'icon',
        type: 'image/x-icon',
        href: '/favicon.ico',
      },
    ],
  },
}

export const nuxtRunTimeConfigOptions = {
  public: {
    baseURL: process.env.APP_BASE_URL,
    baseAPI: process.env.APP_BASE_API,
    baseAPIMock: process.env.APP_BASE_API_MOCK,
    baseInternalAPI: process.env.APP_BASE_INTERNAL_API,
    port: process.env.PORT || '3000',
  },
}
