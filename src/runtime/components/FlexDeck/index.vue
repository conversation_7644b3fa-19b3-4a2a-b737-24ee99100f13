<template>
  <div>
    <div
      v-if="options.isEnabledSearch"
      class="mb-4 flex justify-end"
    >
      <Input
        v-model="q"
        icon="i-heroicons-magnifying-glass"
        :placeholder="options.searchPlaceholder || 'ค้นหา...'"
      />
    </div>

    <slot
      v-if="options.status.isSuccess && options.rawData.length === 0"
      name="empty-state"
    >
      <Empty />
    </slot>

    <div
      v-if="options.pageOptions && options.isEnableInfiniteScroll"
      class="mb-4 flex items-center justify-end"
    >
      <p class="text-xs text-gray-500">
        {{ totalInnerRawData }} รายการ จากทั้งหมด {{ totalCountWithComma }} รายการ
      </p>
    </div>

    <div :class="containerClass">
      <slot
        v-for="(row, index) in displayData"
        :key="index"
        :row="row"
      />
      <div ref="bottomEdgeElement" />
    </div>

    <slot
      v-if="options.status.isLoading"
      name="loading-state"
    >
      <Loader
        :loading="true"
      />
    </slot>

    <div
      v-if="options.pageOptions && !options.isEnableInfiniteScroll && !options.isHidePagination"
      class="mt-4 flex justify-between px-3"
    >
      <p class="text-xs text-gray-500">
        {{ pageBetween }} รายการ จากทั้งหมด {{ totalCountWithComma }} รายการ
      </p>
      <Pagination
        v-if="options.pageOptions.totalPage > 1"
        :to="options.isRouteChange? to : undefined"
        :default-page="options.pageOptions?.currentPage || 1"
        :items-per-page="options.pageOptions.limit"
        :total="options.pageOptions.totalCount"
        @update:page="onPageChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, type PropType, ref, watch } from 'vue'
import { useElementVisibility } from '@vueuse/core'
import type { ButtonProps } from '@nuxt/ui'
import { NumberHelper } from '#core/utils/NumberHelper'
import { _debounce, useRouter, useWatchChange, useWatchTrue } from '#imports'
import { useCoreConfig } from '#core/composables/useConfig'
import type { IFlexDeckOptions } from '#core/components/FlexDeck/types'
import Empty from '#core/components/Empty.vue'

defineSlots<{
  'default': (props: {
    row: any
    key?: number | string
  }) => any
  'empty-state': () => any
  'loading-state': () => any
}>()

const emits = defineEmits<{
  (event: 'pageChange', page: number): void
  (event: 'search', q: string): void
}>()

const props = defineProps({
  options: {
    type: Object as PropType<IFlexDeckOptions>,
    required: true,
  },
  containerClass: {
    type: [String, Array, Object],
    default: '',
  },
})

const coreConfig = useCoreConfig()
const router = useRouter()

const q = ref(props.options?.pageOptions.search ?? '')
const bottomEdgeElement = ref<HTMLElement | null>(null)
const page = ref(props.options.pageOptions?.currentPage || 1)
const innerRawData = ref<object[]>([])

const targetElement = useElementVisibility(bottomEdgeElement)

const displayData = computed(() => {
  return props.options.isEnableInfiniteScroll ? innerRawData.value : props.options.rawData
})

const to = (page: number): ButtonProps['to'] => {
  const params = props.options?.pageOptions?.request?.params || {}

  // Filter out empty values
  const cleanParams = Object.fromEntries(
    Object.entries(params).filter(([key, value]) =>
      value !== null && value !== undefined && value !== '',
    ),
  )

  return {
    query: {
      ...cleanParams,
      page,
    },
  }
}

useWatchChange(() => props.options?.pageOptions?.request?.params, () => {
  if (props.options?.isRouteChange) return

  // Update URL with current query parameters without navigation
  router.replace({
    query: props.options?.pageOptions?.request?.params || {},
  })
})

const pageBetween = computed((): string => {
  const length = props.options.rawData?.length

  if (length === 0) {
    return '0'
  }

  const start = (props.options.pageOptions!.currentPage - 1) * props.options.pageOptions!.limit + 1
  const end = start + length - 1

  return `${start} - ${end}`
})

const totalCountWithComma = computed((): string => {
  return !props.options.pageOptions!.totalCount
    ? '0'
    : NumberHelper.withComma(props.options.pageOptions!.totalCount)
})

const totalInnerRawData = computed((): number => {
  return innerRawData.value?.length || 0
})

watch(
  q,
  _debounce((value) => {
    emits('search', value)
  }, 500),
)

useWatchChange(
  () => props.options.pageOptions?.currentPage,
  (value: number) => {
    page.value = value
  },
)

useWatchTrue(
  () => props.options.status.isSuccess,
  () => {
    if (props.options.isEnableInfiniteScroll) {
      innerRawData.value = [...(innerRawData.value || []), ...props.options.rawData]

      return
    }

    innerRawData.value = props.options.rawData
  },
)

watch(targetElement, (value) => {
  if (props.options.status.isLoading || !props.options.isEnableInfiniteScroll) return

  if (page.value < props.options.pageOptions!.totalPage && value) {
    page.value++
  }
})

const onPageChange = (page: number) => {
  emits('pageChange', page)
}
</script>
