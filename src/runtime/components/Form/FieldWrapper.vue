<template>
  <FormField
    :label="label"
    :name="name"
    :description="description"
    :hint="hint"
    :data-testid="name"
    :help="help"
    :error="errorMessage"
    :required="!!required"
    :ui="containerUi"
  >
    <slot />
  </FormField>
</template>

<script lang="ts" setup>
import type { IFieldProps } from '#core/components/Form/types'

defineProps<IFieldProps & { containerUi?: any }>()
</script>
