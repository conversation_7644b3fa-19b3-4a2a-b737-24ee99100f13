import type { IDateTimeFieldProps } from '../InputDateTime/date_time_field.types'
import type { IFormFieldBase, INPUT_TYPES } from '#core/components/Form/types'

export interface ITimeOption {
  hours?: number | string
  minutes?: number | string
  seconds?: number | string
}

export interface IDateTimeRangeResponse {
  start: Date | string
  end: Date | string
}

export interface IDateTimeRangeFieldProps extends IDateTimeFieldProps {
  isDisabledMultiCalendar?: boolean
}

export type IDateTimeRangeField = IFormFieldBase<
  INPUT_TYPES.DATE_RANGE | INPUT_TYPES.DATE_TIME_RANGE,
  IDateTimeRangeFieldProps,
  {
    change: (value: IDateTimeRangeResponse) => void
  }
>
