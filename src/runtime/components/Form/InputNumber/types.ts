import type {
  IFieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'
import type { InputNumberProps } from '#ui/components/InputNumber.vue'

export interface INumberFieldProps extends IFieldProps {
  orientation?: InputNumberProps['orientation']
  incrementDisabled?: InputNumberProps['incrementDisabled']
  decrementDisabled?: InputNumberProps['decrementDisabled']
  min?: InputNumberProps['min']
  max?: InputNumberProps['max']
  step?: InputNumberProps['step']
  disableWheelChange?: InputNumberProps['disableWheelChange']
  formatOptions?: InputNumberProps['formatOptions']
}

export type INumberField = IFormFieldBase<
  INPUT_TYPES.NUMBER,
  INumberFieldProps,
  {
    change?: (value: string) => void
  }
>
