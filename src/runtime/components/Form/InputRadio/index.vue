<template>
  <FieldWrapper v-bind="wrapperProps">
    <RadioGroup
      :model-value="value"
      :items="options"
      :variant="variant"
      :orientation="orientation"
      :indicator="indicator"
      :legend="legend"
      :disabled="wrapperProps.disabled"
      :required="wrapperProps.required"
      :name="wrapperProps.name"
      value-key="value"
      label-key="label"
      description-key="description"
      :ui="ui"
      @update:model-value="onChange"
    />
  </FieldWrapper>
</template>

<script lang="ts" setup>
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { useFieldHOC } from '#core/composables/useForm'
import type { IRadioFieldProps } from '#core/components/Form/InputRadio/types'

const emits = defineEmits<{
  change: [value: any]
}>()

const props = withDefaults(defineProps<IRadioFieldProps>(), {
  variant: 'list',
  orientation: 'vertical',
  indicator: 'start',
})

const {
  value,
  wrapperProps,
  handleChange,
} = useFieldHOC<any>(props)

const onChange = (newValue: any) => {
  handleChange(newValue)
  emits('change', newValue)
}
</script>
