import type { <PERSON>ieldProps, IFormFieldBase, INPUT_TYPES } from '#core/components/Form/types'
import type { IOption } from '#core/types/common'

export type RadioOption = IOption & {
  label?: string
  value?: any
  description?: string
  disabled?: boolean
  class?: any
}

export interface IRadioFieldProps extends IFieldProps {
  // Visual variants
  variant?: 'list' | 'card' | 'table'

  // Layout
  orientation?: 'horizontal' | 'vertical'
  indicator?: 'start' | 'end' | 'hidden'

  // Data
  options: RadioOption[]

  // Behavior
  legend?: string
}

export type IRadioField = IFormFieldBase<
  INPUT_TYPES.RADIO,
  IRadioFieldProps,
  {
    change?: (value: any) => void
  }
>
