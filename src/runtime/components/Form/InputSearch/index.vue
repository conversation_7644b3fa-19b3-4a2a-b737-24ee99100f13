<template>
  <FieldWrapper v-bind="wrapperProps">
    <Input
      :model-value="value"
      :disabled="wrapperProps.disabled"
      :leading-icon="leadingIcon || searchIcon || 'i-heroicons-magnifying-glass'"
      :trailing-icon="trailingIcon"
      :loading="loading"
      :loading-icon="loadingIcon"
      :name="name"
      :placeholder="wrapperProps.placeholder || 'Search...'"
      type="text"
      :autofocus="!!autoFocus"
      :icon="icon"
      :readonly="readonly"
      :ui="ui"
      @update:model-value="onInput"
    >
      <template #trailing>
        <Button
          v-if="clearable && value && value.length > 0"
          color="neutral"
          class="p-0"
          variant="link"
          :icon="clearIcon || 'i-heroicons-x-mark'"
          :padded="false"
          title="ล้างการค้นหา"
          @click="onClear"
        />
      </template>
    </Input>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import type { ISearchFieldProps } from '#core/components/Form/InputSearch/types'
import { useFieldHOC } from '#core/composables/useForm'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'

const emits = defineEmits(['change', 'search', 'clear'])
const props = withDefaults(defineProps<ISearchFieldProps>(), {
  clearable: true,
})

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<string>(props)

const onInput = (newValue: string) => {
  handleChange(newValue)
  emits('change', newValue)
  emits('search', newValue)
}

const onClear = () => {
  handleChange('')
  emits('change', '')
  emits('clear')
  emits('search', '')
}
</script>
