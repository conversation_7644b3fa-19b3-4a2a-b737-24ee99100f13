import type {
  <PERSON>ieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface ISearchFieldProps extends IFieldProps {
  leadingIcon?: any
  trailingIcon?: any
  loading?: boolean
  loadingIcon?: any
  icon?: string
  clearable?: boolean
  clearIcon?: string
  searchIcon?: string
}

export type ISearchField = IFormFieldBase<
  INPUT_TYPES.SEARCH,
  ISearchFieldProps,
  {
    change?: (value: string) => void
    search?: (value: string) => void
    clear?: () => void
  }
>
