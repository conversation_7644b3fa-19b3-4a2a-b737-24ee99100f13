import type { <PERSON>ieldProps, IFormFieldBase, INPUT_TYPES } from '#core/components/Form/types'
import type { SelectMenuItem } from '#ui/components/SelectMenu.vue'

export type SelectOption = SelectMenuItem & {
  label?: string
  value?: any
}

export interface ISelectFieldProps extends IFieldProps {
  icon?: string
  trailingIcon?: string
  clearIcon?: string
  selectedIcon?: string
  searchInput?: {
    placeholder?: string
    icon?: string
  }
  clearable?: boolean
  loading?: boolean
  options: SelectOption[]
}

export type ISelectField = IFormFieldBase<
  INPUT_TYPES.SELECT,
  ISelectFieldProps,
  {
    change?: (value: string) => void
    search?: (value: string) => void
  }
>
