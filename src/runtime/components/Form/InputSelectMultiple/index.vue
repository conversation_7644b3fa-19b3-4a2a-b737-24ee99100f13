<template>
  <FieldWrapper v-bind="wrapperProps">
    <SelectMenu
      :model-value="value"
      :items="options"
      multiple
      :placeholder="wrapperProps.placeholder"
      :disabled="wrapperProps.disabled"
      :loading="loading"
      :search-input="searchInput"
      :selected-icon="selectedIcon"
      value-key="value"
      label-key="label"
      :icon="icon"
      :ui="ui"
      :ignore-filter="!!$attrs.onSearch"
      @update:model-value="onChange"
      @update:searchTerm="onSearch"
    >
      <template #default="{ modelValue }">
        <div
          v-if="!ArrayHelper.isEmpty(value)"
          :class="theme.tagsWrapper({
            class: [ui?.tagsWrapper],
          })"
        >
          <div
            v-for="_value in ArrayHelper.toArray(modelValue)"
            :key="_value"
            :class="theme.tagsItem({
              class: [ui?.tagsItem],
            })"
          >
            <div
              :class="theme.tagsItemText({
                class: [ui?.tagsItemText],
              })"
            >
              {{ options.find((item) => item.value === _value)?.label || _value }}
              <Icon
                :name="theme.tagsItemDeleteIcon({
                  class: [ui?.tagsItemDeleteIcon],
                })"
                :class="theme.tagsItemDelete({
                  class: [ui?.tagsItemDelete],
                })"
                @click.stop="handleDelete(_value)"
              />
            </div>
          </div>
        </div>
      </template>
    </SelectMenu>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { ArrayHelper } from '#core/utils/ArrayHelper'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { useFieldHOC } from '#core/composables/useForm'
import type { ISelectFieldProps } from '#core/components/Form/InputSelect/types'
import { useUiConfig } from '#core/composables/useConfig'
import { selectMenuTheme } from '#core/theme/selectMenu'

const emits = defineEmits([
  'change',
  // 'search',
])

const props = withDefaults(defineProps<ISelectFieldProps>(), {})
const theme = computed(() => useUiConfig(selectMenuTheme, 'select')())
const {
  value, wrapperProps, handleChange,
} = useFieldHOC<any[]>(props)

const onChange = (value: any) => {
  handleChange(value)
  emits('change', value)
}

const onSearch = (value: any) => {
  // eslint-disable-next-line
  emits('search', value)
}

const handleDelete = (_value: any) => {
  value.value = ArrayHelper.toArray(value.value).filter((item: any) => item !== _value)
}
</script>
