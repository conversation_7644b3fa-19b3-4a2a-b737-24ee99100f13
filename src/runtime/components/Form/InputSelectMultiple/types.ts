import type { <PERSON>ieldProps, IFormFieldBase, INPUT_TYPES } from '#core/components/Form/types'
import type { SelectOption } from '#core/components/Form/InputSelect/types'

export interface ISelectMultipleFieldProps extends IFieldProps {
  icon?: string
  trailingIcon?: string
  selectedIcon?: string
  searchInput?: {
    placeholder?: string
    icon?: string
  }
  loading?: boolean
  options: SelectOption[]
}

export type ISelectMultipleField = IFormFieldBase<
  INPUT_TYPES.SELECT_MULTIPLE,
  ISelectMultipleFieldProps,
  {
    change?: (value: string) => void
    search?: (value: string) => void
  }
>
