<template>
  <FieldWrapper v-bind="wrapperProps">
    <div class="relative">
      <Input
        v-if="type === 'password'"
        ref="inputRef"
        v-maska="activeMaskOptions"
        :model-value="value"
        :disabled="wrapperProps.disabled"
        :leading-icon="leadingIcon"
        :trailing-icon="trailingIcon"
        :loading="loading"
        :loading-icon="loadingIcon"
        :name="name"
        :placeholder="wrapperProps.placeholder"
        :type="isShowPassword ? 'text' : 'password'"
        :autofocus="!!autoFocus"
        :icon="icon"
        :readonly="readonly"
        :ui="defu(ui, { icon: { trailing: { pointer: '' } } })"
        @update:model-value="onChange"
        @focus="onFocus"
        @blur="onBlur"
        @keydown="onKeydown"
      >
        <template #trailing>
          <Button
            color="neutral"
            variant="link"
            :icon="isShowPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
            :padded="false"
            @click="isShowPassword = !isShowPassword"
          />
        </template>
      </Input>
      <Input
        v-else
        ref="inputRef"
        v-maska="activeMaskOptions"
        :model-value="value"
        :disabled="wrapperProps.disabled"
        :leading-icon="leadingIcon"
        :trailing-icon="trailingIcon"
        :loading="loading"
        :loading-icon="loadingIcon"
        :name="name"
        :placeholder="wrapperProps.placeholder"
        :type="type"
        :autofocus="!!autoFocus"
        :icon="icon"
        :readonly="readonly"
        :ui="ui"
        @update:model-value="onChange"
        @focus="onFocus"
        @blur="onBlur"
        @keydown="onKeydown"
      />
      <div
        v-if="showSuggestions && filteredSuggestions.length > 0"
        ref="suggestionsContainerRef"
        :class="theme.suggestionsContainer()"
      >
        <div
          v-for="(suggestion, index) in filteredSuggestions"
          :key="suggestion"
          :ref="(el) => setSuggestionItemRef(el as HTMLElement | null, index)"
          :class="[
            theme.suggestionItem(),
            { [theme.suggestionItemActive()]: index === selectedSuggestionIndex },
          ]"
          @mousedown.prevent="selectSuggestion(suggestion, index)"
          @mouseenter="selectedSuggestionIndex = index"
        >
          {{ suggestion }}
        </div>
      </div>
    </div>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { vMaska } from 'maska/vue'
import type { MaskInputOptions } from 'maska'
import { defu } from 'defu'
import { ref, computed, nextTick, useUiConfig } from '#imports'
import type { ITextFieldProps } from '#core/components/Form/InputText/types'
import { useFieldHOC } from '#core/composables/useForm'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { inputTheme } from '#core/theme/input'

const emits = defineEmits(['change', 'selected'])
const props = withDefaults(defineProps<ITextFieldProps>(), {
  type: 'text',
})

const theme = computed(() => useUiConfig(inputTheme, 'input')())

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<string>(props)

const isShowPassword = ref(false)
const inputRef = ref()
const showSuggestions = ref(false)
const selectedSuggestionIndex = ref(-1)
const suggestionsContainerRef = ref<HTMLElement>()
const suggestionItemRefs = ref<(HTMLElement | null)[]>([])

const setSuggestionItemRef = (el: HTMLElement | null, index: number) => {
  if (suggestionItemRefs.value) {
    suggestionItemRefs.value[index] = el
  }
}

const onChange = (value: any) => {
  if (props.suggestions && props.suggestions.length > 0) {
    showSuggestions.value = true
    selectedSuggestionIndex.value = -1
  }

  handleChange(value)
  emits('change', value)
}

// Computed property for filtered suggestions
const filteredSuggestions = computed(() => {
  if (!props.suggestions || !value.value) {
    return props.suggestions || []
  }

  const inputValue = value.value.toLowerCase()

  return props.suggestions.filter((suggestion) =>
    suggestion.toLowerCase().includes(inputValue),
  )
})

// Event handlers for suggestions
const onFocus = () => {
  if (props.suggestions && props.suggestions.length > 0) {
    showSuggestions.value = true
    selectedSuggestionIndex.value = -1
  }
}

const onBlur = (event: FocusEvent) => {
  // Delay hiding suggestions to allow for mousedown events
  setTimeout(() => {
    showSuggestions.value = false
    selectedSuggestionIndex.value = -1
  }, 150)
}

const onKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value || filteredSuggestions.value.length === 0) {
    return
  }

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedSuggestionIndex.value
        = selectedSuggestionIndex.value < filteredSuggestions.value.length - 1
          ? selectedSuggestionIndex.value + 1
          : 0

      scrollToSelectedSuggestion()

      break
    case 'ArrowUp':
      event.preventDefault()
      selectedSuggestionIndex.value
        = selectedSuggestionIndex.value > 0
          ? selectedSuggestionIndex.value - 1
          : filteredSuggestions.value.length - 1

      scrollToSelectedSuggestion()

      break
    case 'Enter':
      if (selectedSuggestionIndex.value === -1) {
        return
      }

      event.preventDefault()

      if (selectedSuggestionIndex.value >= 0) {
        selectSuggestion(filteredSuggestions.value[selectedSuggestionIndex.value], selectedSuggestionIndex.value)
      }

      break
    case 'Escape':
      showSuggestions.value = false
      selectedSuggestionIndex.value = -1

      break
  }
}

const selectSuggestion = (suggestion: string, index?: number) => {
  // Scroll to selected suggestion if index is provided
  if (index !== undefined) {
    scrollToSuggestionByIndex(index)
  }

  handleChange(suggestion)
  emits('selected', suggestion)
  emits('change', suggestion)
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1

  // Focus back to input
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.$el.querySelector('input')?.focus()
    }
  })
}

const scrollToSelectedSuggestion = () => {
  nextTick(() => {
    if (selectedSuggestionIndex.value >= 0) {
      scrollToSuggestionByIndex(selectedSuggestionIndex.value)
    }
  })
}

const scrollToSuggestionByIndex = (index: number) => {
  if (!suggestionsContainerRef.value || !suggestionItemRefs.value[index]) {
    return
  }

  const container = suggestionsContainerRef.value
  const item = suggestionItemRefs.value[index]

  if (item) {
    const containerRect = container.getBoundingClientRect()
    const itemRect = item.getBoundingClientRect()

    // Check if item is visible within container
    const isAboveView = itemRect.top < containerRect.top
    const isBelowView = itemRect.bottom > containerRect.bottom

    if (isAboveView || isBelowView) {
      // Scroll the item into view
      item.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest',
      })
    }
  }
}

const activeMaskOptions = computed<string | MaskInputOptions | undefined>(
  () => {
    // Priority 1: If maskOptions prop is provided and is not empty, use it.
    if (props.maskOptions && Object.keys(props.maskOptions).length > 0) {
      // Check if maskOptions can be simplified to a string mask
      if (
        typeof props.maskOptions.mask === 'string'
        && Object.keys(props.maskOptions).length === 1
      ) {
        return props.maskOptions.mask
      }

      return props.maskOptions
    }

    // Priority 2: Construct options from individual props.
    const options: MaskInputOptions = {}
    let hasIndividualProps = false

    if (props.mask !== undefined) {
      // props.mask type is MaskType (string | readonly string[] | null)
      options.mask = props.mask
      hasIndividualProps = true
    }

    if (props.maskTokens !== undefined) {
      options.tokens = props.maskTokens
      hasIndividualProps = true
    }

    if (props.maskEager !== undefined) {
      options.eager = props.maskEager
      hasIndividualProps = true
    }

    if (props.maskReversed !== undefined) {
      options.reversed = props.maskReversed
      hasIndividualProps = true
    }

    if (props.maskTokensReplace !== undefined) {
      options.tokensReplace = props.maskTokensReplace
      hasIndividualProps = true
    }

    if (hasIndividualProps) {
      const keys = Object.keys(options)

      // If only props.mask is a string, and no other individual props are set, return the mask string directly.
      if (
        keys.length === 1
        && keys[0] === 'mask'
        && typeof options.mask === 'string'
      ) {
        return options.mask
      }

      // Otherwise, return the full options object.
      // This correctly handles:
      // - props.mask is string[] or null (will be wrapped in options object: { mask: [...] } or { mask: null })
      // - props.mask is a string but other props like eager, tokens are also set.
      return options
    }

    return undefined // No mask to apply
  },
)
</script>
