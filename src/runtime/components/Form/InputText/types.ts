import type { MaskInputOptions, MaskType } from 'maska'
import type {
  <PERSON>ieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface ITextFieldProps extends IFieldProps {
  type?: 'text' | 'password' | 'email'
  leadingIcon?: any
  trailingIcon?: any
  loading?: boolean
  loadingIcon?: any
  icon?: string
  mask?: MaskType
  maskOptions?: MaskInputOptions
  maskTokens?: MaskInputOptions['tokens']
  maskEager?: boolean
  maskTokensReplace?: boolean
  maskReversed?: boolean
  suggestions?: string[]
}

export type ITextField = IFormFieldBase<
  INPUT_TYPES.TEXT | INPUT_TYPES.PASSWORD | INPUT_TYPES.EMAIL,
  ITextFieldProps,
  {
    change?: (value: string) => void
    selected?: (value: string) => void
  }
>
