<template>
  <FieldWrapper
    v-bind="wrapperProps"
    label=""
    description=""
  >
    <Switch
      :model-value="value"
      :disabled="wrapperProps.disabled"
      :name="name"
      :ui="ui"
      :label="label"
      :description="description"
      :loading="loading"
      :loading-icon="loadingIcon"
      @update:modelValue="onChange"
    />
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { useFieldHOC } from '#core/composables/useForm'
import type { IToggleFieldProps } from '#core/components/Form/InputToggle/types'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'

const emit = defineEmits(['change'])

const props = withDefaults(defineProps<IToggleFieldProps>(), {})
const {
  value, wrapperProps,
} = useFieldHOC<boolean>(props)

const onChange = (bool: boolean) => {
  emit('change', bool)
  value.value = bool
}
</script>
