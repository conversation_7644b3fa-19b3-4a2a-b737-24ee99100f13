<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      ref="dropzoneRef"
      :class="theme.base()"
    >
      <div :class="theme.wrapper()">
        <!-- Empty State -->
        <EmptyState
          v-if="uploadState.isEmpty.value"
          :theme="theme"
          :select-file-label="selectFileLabel"
          :select-file-sub-label="selectFileSubLabel"
          :placeholder="placeholder"
          @open-file="uploadState.handleOpenFile"
        />

        <!-- Success State -->
        <SuccessState
          v-if="uploadState.isSuccess.value"
          :theme="theme"
          :value="value"
          :disabled="wrapperProps.disabled"
          :readonly="wrapperProps.readonly"
          @preview="uploadState.handlePreview"
          @download="handleDownloadFile"
          @delete="uploadState.handleDeleteFile"
        />
      </div>
    </div>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import EmptyState from '../fileState/EmptyState.vue'
import SuccessState from '../fileState/SuccessState.vue'
import { useFileState } from '../fileState/useFileState'

import type { IUploadDropzoneProps } from './types'
import type { IFileValue } from '#core/components/Form/types'
import { computed, useTemplateRef, useWatchChange } from '#imports'

import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { useFieldHOC } from '#core/composables/useForm'
import { uploadFileDropzoneTheme } from '#core/theme/uploadFileDropzone'
import { useUiConfig } from '#core/composables/useConfig'

const props = withDefaults(defineProps<IUploadDropzoneProps>(), {
  selectFileLabel: 'คลิกเพื่อเลือกไฟล์',
  selectFileSubLabel: 'หรือ ลากและวางที่นี่',
})

const emits = defineEmits<{
  change: [value: File | undefined]
  delete: []
}>()

// Core form integration
const {
  wrapperProps,
  handleChange: onChange,
  setErrors,
  value,
} = useFieldHOC<IFileValue>(props)

// Computed properties
const acceptedFileTypes = computed(() =>
  typeof props.accept === 'string' ? props.accept : props.accept?.join(','),
)

// Dropzone ref
const dropzoneRef = useTemplateRef('dropzoneRef')

// Upload state management
const uploadState = useFileState(
  props,
  emits,
  onChange,
  setErrors,
  value,
  acceptedFileTypes,
  wrapperProps,
  dropzoneRef,
)

useWatchChange(() => value.value, (newValue) => {
  if (typeof newValue === 'object' && newValue !== null) {
    // Ensure the value is a File object
    onChange(newValue as File)
  } else {
    onChange(undefined)
  }
})

// Theme configuration
const theme = computed(() =>
  useUiConfig(uploadFileDropzoneTheme, 'uploadFileDropzone')({
    dragover: uploadState.dropzone.isOverDropZone.value && uploadState.isEmpty.value,
    disabled: wrapperProps.value.disabled,
  }),
)

// Download handler for local files
const handleDownloadFile = (): void => {
  if (value.value?.url && value.value?.name) {
    const a = document.createElement('a')

    a.href = value.value.url
    a.download = value.value.name
    a.click()
  }
}
</script>
