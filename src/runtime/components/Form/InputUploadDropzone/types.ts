import type { <PERSON>ieldProps, IFormFieldBase, INPUT_TYPES } from '../types'

export interface IUploadDropzoneProps extends IFieldProps {
  // File Validation
  accept?: string[] | string
  maxSize?: number // in kb

  // UI Labels
  selectFileLabel?: string // default = คลิกเพื่อเลือกไฟล์
  selectFileSubLabel?: string // default = หรือ ลากและวางที่นี่
}

export type IUploadDropzoneField = IFormFieldBase<
  INPUT_TYPES.UPLOAD_DROPZONE,
  IUploadDropzoneProps,
  {
    change: (value: File | undefined) => void
    delete: () => void
  }
>
