<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      ref="dropzoneRef"
      :class="theme.base()"
    >
      <div :class="theme.wrapper()">
        <!-- Empty State -->
        <EmptyState
          v-if="uploadState.isEmpty.value"
          :theme="theme"
          :select-file-label="selectFileLabel"
          :select-file-sub-label="selectFileSubLabel"
          :placeholder="placeholder"
          @open-file="uploadState.handleOpenFile"
        />

        <!-- Loading State -->
        <LoadingState
          v-if="uploadState.isUploading.value"
          :theme="theme"
          :selected-file="uploadState.selectedFile.value!"
          :percent="uploadState.percent.value"
          :uploading-label="uploadingLabel"
        />

        <!-- Success State -->
        <SuccessState
          v-if="uploadState.isSuccess.value"
          :theme="theme"
          :value="value"
          :disabled="wrapperProps.disabled"
          :readonly="wrapperProps.readonly"
          @preview="uploadState.handlePreview"
          @download="handleDownloadFile"
          @delete="uploadState.handleDeleteFile"
        />

        <!-- Failed State -->
        <FailedState
          v-if="uploadState.isError.value"
          :theme="theme"
          :selected-file="uploadState.selectedFile.value!"
          :upload-failed-label="uploadFailedLabel"
          :retry-label="retryLabel"
          @retry="uploadState.handleRetryUpload"
          @delete="uploadState.handleDeleteFile"
        />
      </div>
    </div>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import EmptyState from '../fileState/EmptyState.vue'
import SuccessState from '../fileState/SuccessState.vue'
import LoadingState from '../fileState/LoadingState.vue'
import FailedState from '../fileState/FailedState.vue'
import { useUploadState } from '../fileState/useUploadState'

import type { IUploadDropzoneAutoProps } from './types'
import type { IFileValue } from '#core/components/Form/types'
import { computed, useTemplateRef } from '#imports'

import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { useFieldHOC } from '#core/composables/useForm'
import { uploadFileDropzoneTheme } from '#core/theme/uploadFileDropzone'
import { useUiConfig } from '#core/composables/useConfig'
import { downloadFileFromURL } from '#core/helpers/componentHelper'

const props = withDefaults(defineProps<IUploadDropzoneAutoProps>(), {
  bodyKey: 'file',
  responseURL: 'url',
  responsePath: 'path',
  responseName: 'name',
  responseSize: 'size',
  selectFileLabel: 'คลิกเพื่อเลือกไฟล์',
  selectFileSubLabel: 'หรือ ลากและวางที่นี่',
  uploadingLabel: 'กำลังอัพโหลด...',
  uploadFailedLabel: 'อัพโหลดล้มเหลว, กรุณาลองอีกครั้ง',
  retryLabel: 'ลองอีกครั้ง',
})

const emits = defineEmits<{
  change: [value: File | undefined]
  success: [res: IFileValue]
  delete: []
}>()

// Core form integration
const {
  wrapperProps,
  handleChange: onChange,
  setErrors,
  value,
} = useFieldHOC<IFileValue>(props)

// Computed properties
const acceptedFileTypes = computed(() =>
  typeof props.accept === 'string' ? props.accept : props.accept?.join(','),
)

// Dropzone ref
const dropzoneRef = useTemplateRef('dropzoneRef')

// Upload state management
const uploadState = useUploadState(
  props,
  emits,
  onChange,
  setErrors,
  value,
  acceptedFileTypes,
  wrapperProps,
  dropzoneRef,
)

// Theme configuration
const theme = computed(() =>
  useUiConfig(uploadFileDropzoneTheme, 'uploadFileDropzone')({
    dragover: uploadState.dropzone.isOverDropZone.value && uploadState.isEmpty.value,
    disabled: wrapperProps.value.disabled,
    failed: uploadState.upload.status.value.isError,
  }),
)

// Download handler (not moved to composable as it uses external helper)
const handleDownloadFile = (): void => {
  if (value.value?.url && value.value?.name) {
    downloadFileFromURL(value.value.url, value.value.name)
  }
}
</script>
