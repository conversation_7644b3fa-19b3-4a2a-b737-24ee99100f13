<template>
  <div class="upload-image-form">
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      @change="handleFileSelect"
    />
    <button
      type="button"
      @click="handleUpload"
    >
      Upload
    </button>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface Props {
  options?: {
    requestOptions?: any
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  submit: [url: string]
}>()

const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    selectedFile.value = file
  }
}

const handleUpload = async () => {
  if (!selectedFile.value) return

  // For now, just create a local URL
  const url = URL.createObjectURL(selectedFile.value)

  emit('submit', url)
}
</script>
