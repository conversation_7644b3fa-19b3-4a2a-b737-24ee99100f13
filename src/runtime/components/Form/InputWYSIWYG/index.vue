<template>
  <FieldWrapper v-bind="wrapperProps">
    <div :class="ui.container()">
      <div
        v-if="showToolbar"
        :class="ui.toolbar()"
      >
        <div
          v-for="(items, index) in menuItems"
          :key="index"
          :class="ui.toolbarGroup()"
        >
          <button
            v-for="item in items"
            :key="item.name"
            :class="[ui.menuItem(), { [ui.menuItemActive()]: item.isActive?.() }]"
            type="button"
            :title="item.title"
            @click="item.action"
          >
            <Icon
              :name="item.icon"
              :class="ui.icon()"
            />
          </button>
        </div>
      </div>
      <ClientOnly>
        <EditorContent
          :editor="editor"
          :class="ui.editorContent()"
        />
        <template #fallback>
          <div
            class="prose min-h-[200px] px-4 py-2"
            v-html="value"
          />
        </template>
      </ClientOnly>
    </div>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import { computed, watch } from 'vue'
import { EditorContent, useEditor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import Youtube from '@tiptap/extension-youtube'
import { useFieldHOC } from '#core/composables/useForm'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { wysiwygTheme } from '#core/theme/wysiwyg'
import { useUiConfig } from '#core/composables/useConfig'
import type { IWYSIWYGFieldProps } from '#core/components/Form/InputWYSIWYG/types'

interface MenuItem {
  name: string
  icon: string
  action: () => void
  isActive?: () => boolean
  title: string
}

const props = withDefaults(defineProps<IWYSIWYGFieldProps>(), {
  size: 'md',
  color: 'gray',
})

const {
  value,
  wrapperProps,
} = useFieldHOC<string>(props)

const ui = computed(() => useUiConfig(wysiwygTheme, 'wysiwyg')({
  size: props.size,
  color: props.color,
}))

const showToolbar = computed(() => {
  if (!props.toolbar) return true

  return Object.values(props.toolbar).some(Boolean)
})

const editor = useEditor({
  content: value.value,
  extensions: [
    StarterKit,
    Underline,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Link.configure({
      openOnClick: false,
    }),
    Image,
    Youtube,
  ],
  editorProps: {
    attributes: {
      class: 'prose px-4 py-2 focus:outline-none min-h-[200px]',
    },
  },
  onUpdate: ({
    editor,
  }) => {
    value.value = editor.getHTML()
  },
})

watch(value, (newValue) => {
  if (editor.value && (newValue !== editor.value.getHTML())) {
    editor.value.commands.setContent(newValue)
  }
})

const toolbarConfig = {
  format: [
    {
      key: 'bold',
      name: 'bold',
      icon: 'ph:text-b-bold',
      action: () => editor.value?.chain().focus().toggleBold().run(),
      isActive: () => editor.value?.isActive('bold') || false,
      title: 'ตัวหนา',
    },
    {
      key: 'italic',
      name: 'italic',
      icon: 'ph:text-italic',
      action: () => editor.value?.chain().focus().toggleItalic().run(),
      isActive: () => editor.value?.isActive('italic') || false,
      title: 'ตัวเอียง',
    },
    {
      key: 'underline',
      name: 'underline',
      icon: 'ph:text-underline',
      action: () => editor.value?.chain().focus().toggleUnderline().run(),
      isActive: () => editor.value?.isActive('underline') || false,
      title: 'ขีดเส้นใต้',
    },
  ],
  list: [
    {
      key: 'bulletList',
      name: 'bullet-list',
      icon: 'ph:list-bullets',
      action: () => editor.value?.chain().focus().toggleBulletList().run(),
      isActive: () => editor.value?.isActive('bulletList') || false,
      title: 'รายการสัญลักษณ์',
    },
    {
      key: 'orderedList',
      name: 'ordered-list',
      icon: 'ph:list-numbers',
      action: () => editor.value?.chain().focus().toggleOrderedList().run(),
      isActive: () => editor.value?.isActive('orderedList') || false,
      title: 'รายการลำดับ',
    },
  ],
  textAlign: [
    {
      key: 'textAlign',
      name: 'align-left',
      icon: 'ph:text-align-left',
      action: () => editor.value?.chain().focus().setTextAlign('left').run(),
      isActive: () => editor.value?.isActive({
        textAlign: 'left',
      }) || false,
      title: 'จัดชิดซ้าย',
    },
    {
      key: 'textAlign',
      name: 'align-center',
      icon: 'ph:text-align-center',
      action: () => editor.value?.chain().focus().setTextAlign('center').run(),
      isActive: () => editor.value?.isActive({
        textAlign: 'center',
      }) || false,
      title: 'จัดกึ่งกลาง',
    },
    {
      key: 'textAlign',
      name: 'align-right',
      icon: 'ph:text-align-right',
      action: () => editor.value?.chain().focus().setTextAlign('right').run(),
      isActive: () => editor.value?.isActive({
        textAlign: 'right',
      }) || false,
      title: 'จัดชิดขวา',
    },
  ],
  media: [
    {
      key: 'link',
      name: 'link',
      icon: 'ph:link-simple',
      action: () => {
        const url = window.prompt('URL')

        if (url) {
          editor.value?.chain().focus().setLink({
            href: url,
          }).run()
        }
      },
      isActive: () => editor.value?.isActive('link') || false,
      title: 'แทรกลิงก์',
    },
    {
      key: 'image',
      name: 'image',
      icon: 'ph:image',
      action: () => {
        const url = window.prompt('URL รูปภาพ')

        if (url) {
          editor.value?.chain().focus().setImage({
            src: url,
          }).run()
        }
      },
      title: 'แทรกรูปภาพ',
    },
    {
      key: 'youtube',
      name: 'video',
      icon: 'ph:video-camera',
      action: () => {
        const url = window.prompt('URL วิดีโอ')

        if (url) {
          editor.value?.chain().focus().setYoutubeVideo({
            src: url,
          }).run()
        }
      },
      title: 'แทรกวิดีโอ',
    },
  ],
  history: [
    {
      key: 'undo',
      name: 'undo',
      icon: 'ph:arrow-counter-clockwise',
      action: () => editor.value?.chain().focus().undo().run(),
      title: 'เลิกทำ',
    },
    {
      key: 'redo',
      name: 'redo',
      icon: 'ph:arrow-clockwise',
      action: () => editor.value?.chain().focus().redo().run(),
      title: 'ทำใหม่',
    },
  ],
}

const menuItems = computed(() => {
  const items: MenuItem[][] = []

  // Create toolbar groups based on enabled options
  Object.entries(toolbarConfig).forEach(([groupKey, groupItems]) => {
    const enabledItems = groupItems.filter((item) => {
      if (groupKey === 'textAlign') {
        return props.toolbar?.textAlign
      }

      return props.toolbar?.[item.key as keyof typeof props.toolbar]
    })

    if (enabledItems.length > 0) {
      items.push(enabledItems)
    }
  })

  return items
})
</script>
