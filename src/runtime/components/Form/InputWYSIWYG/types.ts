import type { Editor } from '@tiptap/vue-3'
import type { <PERSON>ieldProps, IFormFieldBase, INPUT_TYPES } from '#core/components/Form/types'

export interface IWYSIWYGFieldProps extends IFieldProps {
  // Editor configuration
  editable?: boolean
  autofocus?: boolean

  // Content
  content?: string

  // Toolbar configuration
  toolbar?: {
    bold?: boolean
    italic?: boolean
    underline?: boolean
    strike?: boolean
    code?: boolean
    heading?: boolean | number[]
    paragraph?: boolean
    bulletList?: boolean
    orderedList?: boolean
    blockquote?: boolean
    codeBlock?: boolean
    horizontalRule?: boolean
    link?: boolean
    image?: boolean
    youtube?: boolean
    textAlign?: boolean
    undo?: boolean
    redo?: boolean
  }

  // Styling
  minHeight?: string | number
  maxHeight?: string | number
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'gray'
  // Image upload
  imageUpload?: {
    enabled?: boolean
    uploadUrl?: string
    maxSize?: number
    accept?: string[]
    headers?: Record<string, string>
  }

  // Link configuration
  linkOptions?: {
    openOnClick?: boolean
    HTMLAttributes?: Record<string, any>
  }

  // Styling
  containerUi?: any

  // Image upload via modal
  image?: {
    requestOptions?: any
  }
}

export type IWYSIWYGField = IFormFieldBase<
  INPUT_TYPES.WYSIWYG,
  IWYSIWYGFieldProps,
  {
    change?: (content: string) => void
    update?: (content: string, editor: Editor) => void
    focus?: (editor: Editor) => void
    blur?: (editor: Editor) => void
  }
>
