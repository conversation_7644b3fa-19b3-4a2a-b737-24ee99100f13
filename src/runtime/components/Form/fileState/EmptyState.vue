<template>
  <div :class="theme.placeholderWrapper()">
    <Icon
      :name="icons.uploadIcon"
      :class="theme.labelIcon()"
    />
    <div :class="theme.labelWrapper()">
      <p
        class="text-primary cursor-pointer font-bold"
        @click="$emit('openFile')"
      >
        {{ selectFileLabel }}
      </p>
      <p>{{ selectFileSubLabel }}</p>
    </div>
    <p
      v-if="placeholder"
      :class="theme.placeholder()"
    >
      {{ placeholder }}
    </p>
  </div>
</template>

<script lang="ts" setup>
import { useUiIconConfig } from '#core/composables/useConfig'

interface Props {
  theme: any
  selectFileLabel: string
  selectFileSubLabel: string
  placeholder?: string
}

defineProps<Props>()

defineEmits<{
  openFile: []
}>()

const icons = useUiIconConfig('uploadFileDropzone')
</script>
