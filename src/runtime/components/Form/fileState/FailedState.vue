<template>
  <div :class="theme.onFailedWrapper()">
    <div :class="theme.onFailedFailedImgWrapper()">
      <Icon
        :name="getFileIcon(selectedFile)"
        :class="theme.onFailedFailedIconClass()"
      />
    </div>
    <div :class="theme.onFailedTextWrapper()">
      <div class="truncate">
        <h1 class="truncate font-bold">
          {{ selectedFile.name }}
        </h1>
        <p class="text-error truncate font-light">
          {{ uploadFailedLabel }}
        </p>
        <Button
          variant="link"
          :icon="icons.actionRetryIcon"
          :class="theme.actionRetryBtnClass()"
          color="primary"
          @click="$emit('retry')"
        >
          {{ retryLabel }}
        </Button>
      </div>
      <Icon
        :name="icons.actionDeleteIcon"
        :class="theme.actionDeleteIconClass()"
        title="ลบไฟล์"
        @click="$emit('delete')"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { isImage } from '#core/helpers/componentHelper'
import { useUiIconConfig } from '#core/composables/useConfig'

interface Props {
  theme: any
  selectedFile: File
  uploadFailedLabel: string
  retryLabel: string
}

const props = defineProps<Props>()

defineEmits<{
  retry: []
  delete: []
}>()

const icons = useUiIconConfig('uploadFileDropzone')

const getFileIcon = (file: File): string => {
  if (isImage(file)) {
    return icons.placeholderImgIcon
  }

  return icons.filePreviewIcon
}
</script>
