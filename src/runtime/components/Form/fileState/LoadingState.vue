<template>
  <div :class="theme.onLoadingWrapper()">
    <div :class="theme.onLoadingPlaceholderWrapper()">
      <Icon
        :name="getFileIcon(selectedFile)"
        :class="theme.onLoadingPlaceholderIconClass()"
      />
    </div>
    <div :class="theme.onLoadingTextWrapper()">
      <div class="truncate">
        <h1 class="truncate font-bold">
          {{ selectedFile.name }}
        </h1>
        <p class="truncate font-light text-gray-400">
          {{ getFileSize(selectedFile) }} - {{ percent }}% {{ uploadingLabel }}
        </p>
      </div>
      <div>
        <Icon
          :name="icons.loadingIcon"
          :class="theme.onLoadingLoadingIconClass()"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { isImage } from '#core/helpers/componentHelper'
import { useUiIconConfig } from '#core/composables/useConfig'

interface Props {
  theme: any
  selectedFile: File
  percent: number
  uploadingLabel: string
}

const props = defineProps<Props>()
const icons = useUiIconConfig('uploadFileDropzone')

const getFileIcon = (file: File): string => {
  if (isImage(file)) {
    return icons.placeholderImgIcon
  }

  return icons.filePreviewIcon
}

const getFileSize = (file: File): string => {
  const size = file.size
  const useMb = size > 1024 * 1024

  if (useMb) {
    const sizeMb = (size / (1024 * 1024)).toFixed(2)

    return `${sizeMb} MB`
  }

  const sizeKb = (size / 1024).toFixed(2)

  return `${sizeKb} KB`
}
</script>
