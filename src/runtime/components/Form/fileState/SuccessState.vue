<template>
  <div :class="theme.onPreviewWrapper()">
    <div
      v-if="isImageFromPath(value.path)"
      :class="theme.onPreviewImgWrapper()"
    >
      <div
        class="size-full overflow-hidden"
      >
        <img
          :src="value.url"
          :class="theme.onPreviewImgClass()"
          alt="img-preview"
        />
      </div>
    </div>
    <div
      v-else
      :class="theme.onPreviewFileWrapper()"
    >
      <div>
        <Icon
          :name="icons.filePreviewIcon"
          :class="theme.onPreviewFileClass()"
        />
      </div>
    </div>
    <div :class="theme.onPreviewTextWrapper()">
      <div class="truncate">
        <h1 class="truncate font-bold">
          {{ value.name }}
        </h1>
        <p class="truncate text-sm font-light text-gray-400">
          {{ getFileSizeFromValue(value) }}
        </p>
      </div>
      <div :class="theme.actionWrapper()">
        <a
          v-if="isPDFFromPath(value.path)"
          :href="value.url"
          target="_blank"
          class="flex"
        >
          <Icon
            :name="icons.actionPreviewIcon"
            :class="theme.actionIconClass()"
            title="ดูตัวอย่าง"
          />
        </a>
        <Icon
          v-if="isImageFromPath(value.path) || isVideoFromPath(value.path)"
          :name="icons.actionPreviewIcon"
          :class="theme.actionIconClass()"
          title="ดูตัวอย่าง"
          @click="$emit('preview')"
        />
        <Icon
          :name="icons.actionDownloadIcon"
          :class="theme.actionIconClass()"
          title="ดาวน์โหลดไฟล์"
          @click="$emit('download')"
        />
        <Icon
          v-if="!disabled && !readonly"
          :name="icons.actionDeleteIcon"
          :class="theme.actionIconClass()"
          title="ลบไฟล์"
          @click="$emit('delete')"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  isImageFromPath,
  isPDFFromPath,
  isVideoFromPath,
  useFileSize,
} from '#core/helpers/componentHelper'
import type { IFileValue } from '#core/components/Form/types'
import { useUiIconConfig } from '#core/composables/useConfig'

interface Props {
  theme: any
  value: IFileValue
  disabled?: boolean
  readonly?: boolean
}

defineProps<Props>()

defineEmits<{
  preview: []
  download: []
  delete: []
}>()

const icons = useUiIconConfig('uploadFileDropzone')

const getFileSizeFromValue = (fileValue: IFileValue): string => {
  const allocate = useFileSize(fileValue.size || 0)

  return allocate.isSelectedFileUseMb.value
    ? `${allocate.selectedFileSizeMb.value} MB`
    : `${allocate.selectedFileSizeKb.value} KB`
}
</script>
