import { useDropZone, useFileDialog } from '@vueuse/core'

import type { TemplateRef } from 'vue'
import PreviewModal from '../fileState/PreviewModal.vue'
import type { IUploadDropzoneProps } from '../InputUploadDropzone/types'
import type { IFileValue } from '#core/components/Form/types'
import { computed, ref, useOverlay } from '#imports'
import { useFileAllocate } from '#core/helpers/componentHelper'

const overlay = useOverlay()
const previewModal = overlay.create(PreviewModal)

export enum UploadState {
  EMPTY = 'empty',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error',
}

export const useFileState = (
  props: IUploadDropzoneProps,
  emits: any,
  onChange: (value: IFileValue | undefined) => void,
  setErrors: (error: string) => void,
  value: any,
  acceptedFileTypes: any,
  wrapperProps: any,
  dropzoneRef: TemplateRef<HTMLDivElement | undefined>,
) => {
  // Refs
  const selectedFile = ref<File>()
  const currentObjectUrl = ref<string>('')
  const fileAllocate = useFileAllocate(selectedFile, props)

  // File validation
  const validateFile = (file: File): boolean => {
    if (props.accept && fileAllocate.acceptFile.value) {
      const acceptedTypes = fileAllocate.acceptFile.value
      const acceptedTypesList = acceptedTypes.split(',').map((type) => type.trim())

      const fileExtension = file.name.toLowerCase().split('.').pop()

      const isValidFileType = acceptedTypesList.some((acceptedType) => {
        if (acceptedType.startsWith('.')) {
          const extension = acceptedType.slice(1).toLowerCase()

          return fileExtension === extension
        } else if (!acceptedType.includes('/') && !acceptedType.includes('*')) {
          return fileExtension === acceptedType.toLowerCase()
        }

        if (acceptedType.endsWith('/*')) {
          const baseType = acceptedType.slice(0, -2)

          return file.type.startsWith(baseType + '/')
        }

        return file.type === acceptedType
      })

      if (!isValidFileType) {
        setErrors('ประเภทไฟล์ไม่ถูกต้อง (รองรับเฉพาะ ' + acceptedTypesList.join(', ') + ')')

        return false
      }
    }

    // Check file size
    if (props.maxSize) {
      const maxSizeBytes = (fileAllocate.acceptFileSizeKb.value || 0) * 1024

      if (file.size > maxSizeBytes) {
        if (fileAllocate.isAcceptFileUseMb.value) {
          setErrors(`ขนาดไฟล์ต้องไม่เกิน ${fileAllocate.acceptFileSizeMb.value} MB`)
        } else {
          setErrors(`ขนาดไฟล์ต้องไม่เกิน ${fileAllocate.acceptFileSizeKb.value} KB`)
        }

        return false
      }
    }

    setErrors('')

    return true
  }

  // File processing
  const processFile = (file: File): void => {
    if (!validateFile(file)) return

    selectedFile.value = file
    emits('change', file)

    // Revoke previous object URL if it exists
    if (currentObjectUrl.value) {
      URL.revokeObjectURL(currentObjectUrl.value)
    }

    // Create a local object URL for the file
    const objectUrl = URL.createObjectURL(file)

    currentObjectUrl.value = objectUrl

    // Set the value and emit success
    value.value = file
    emits('success', file)
  }

  // Event handlers
  const handleFileDrop = (files: File[] | null): void => {
    if (wrapperProps.value.disabled || wrapperProps.value.readonly || !files?.length || !isEmpty.value) return

    const file = files[0]

    if (file) {
      processFile(file)
    }
  }

  // File dialog configuration
  const fileDialog = useFileDialog({
    accept: acceptedFileTypes.value || '',
    directory: false,
    multiple: false,
  })

  // Dropzone configuration
  const dropzone = useDropZone(dropzoneRef, {
    onDrop: handleFileDrop,
    // dataTypes: typeof props.accept === 'string' ? [props.accept] : props.accept,
    multiple: false,
    preventDefaultForUnhandled: false,
  })

  // Computed current state
  const currentState = computed(() => {
    if (value.value) return UploadState.SUCCESS

    return UploadState.EMPTY
  })

  // State checks
  const isEmpty = computed(() => currentState.value === UploadState.EMPTY)
  const isSuccess = computed(() => currentState.value === UploadState.SUCCESS)

  const handleInputChange = (event: Event): void => {
    if (wrapperProps.value.disabled || wrapperProps.value.readonly) return

    const file = (event.target as HTMLInputElement).files?.[0]

    if (file) {
      processFile(file)
    }
  }

  fileDialog.onChange((files) => {
    if (files?.length) {
      processFile(files[0])
    }
  })

  const handleOpenFile = (): void => {
    if (wrapperProps.value.disabled || wrapperProps.value.readonly) return
    fileDialog.open()
  }

  const handleDeleteFile = (): void => {
    fileDialog.reset()

    if (currentObjectUrl.value) {
      URL.revokeObjectURL(currentObjectUrl.value)
      currentObjectUrl.value = ''
    }

    selectedFile.value = undefined
    onChange(undefined)
    emits('delete')
  }

  const handleRetryUpload = (): void => {
    if (selectedFile.value) {
      processFile(selectedFile.value)
    }
  }

  const handlePreview = (): void => {
    previewModal.open({
      value: value.value,
    })
  }

  // Cleanup function to revoke object URL when component is unmounted
  const cleanup = () => {
    if (currentObjectUrl.value) {
      URL.revokeObjectURL(currentObjectUrl.value)
      currentObjectUrl.value = ''
    }
  }

  // Call cleanup when component is unmounted
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', cleanup)
  }

  return {
    // State
    currentState,
    isEmpty,
    isSuccess,

    selectedFile,
    currentObjectUrl,

    // Upload utilities
    dropzone,

    // Handlers
    handleInputChange,
    handleOpenFile,
    handleDeleteFile,
    handleRetryUpload,
    handlePreview,

    // Cleanup
    cleanup,
  }
}
