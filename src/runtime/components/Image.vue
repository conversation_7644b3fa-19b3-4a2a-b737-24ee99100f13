<template>
  <UseImage v-bind="$props">
    <template #loading>
      <slot name="loading">
        <div
          class="flex h-full w-full items-center justify-center"
        >
          <Loader
            :loading="true"
          />
        </div>
      </slot>
    </template>

    <template #error>
      <slot name="error">
        <div
          class="flex h-full w-full items-center justify-center"
        >
          <p class="text-error-400">
            <Icon
              name="i-heroicons:exclamation-circle-solid"
              class="text-error-400 size-8"
            />
          </p>
        </div>
      </slot>
    </template>
  </UseImage>
</template>

<script lang="ts" setup>
import type { UseImageOptions } from '@vueuse/core'
import { UseImage } from '@vueuse/components'

defineSlots<{
  loading: () => any
  error: () => any
}>()

withDefaults(defineProps<UseImageOptions>(), {})
</script>
