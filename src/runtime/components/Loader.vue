<template>
  <slot
    v-if="loading"
    name="loading"
  >
    <div

      :class="theme.base({
        class: [ui?.base, props.class],
      })"
    >
      <Icon
        :name="icon"
        :class="[theme.icon({
          class: [ui?.icon],
        })]"
      />
    </div>
  </slot>
  <slot v-else />
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { loaderTheme } from '#core/theme/loader'
import { useUiConfig } from '#core/composables/useConfig'

defineSlots<{
  loading: () => any
}>()

const props = withDefaults(defineProps<{
  loading?: boolean
  icon?: string
  ui?: typeof loaderTheme['slots']
  class?: any
}>(), {
  loading: true,
  icon: 'i-svg-spinners:180-ring-with-bg',
})

const theme = computed(() => useUiConfig(loaderTheme, 'loader')())
</script>
