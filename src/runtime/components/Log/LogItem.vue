<template>
  <div class="text-xs">
    <div class="mb-2 flex items-center justify-between">
      <p
        class="font-semibold"
      >
        {{ title || 'Log' }}
      </p>
      <Icon
        class="cursor-pointer"
        :name="isShow ? 'heroicons:minus' : 'heroicons:plus'"
        @click="isShow = !isShow"
      />
    </div>
    <VCodeBlock
      v-show="isShow"
      :code="getCode"
      max-height="500"
      :browser-window="false"
      :indent="2"
      lang="json"
      highlightjs
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { VCodeBlock } from '@wdns/vue-code-block'

const props = defineProps<{
  data?: any
  title?: string
}>()

const isShow = ref(true)

const getCode = computed(() => {
  try {
    return JSON.stringify(props.data)
  } catch (e) {
    return JSON.stringify('{}')
  }
})
</script>

<style>
.hljs-string {
  white-space: break-spaces;
}
</style>
