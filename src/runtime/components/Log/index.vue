<template>
  <DevOnly>
    <TeleportSafe
      to="#dev-logs"
    >
      <LogItem
        v-if="typeof data !== 'undefined'"
        :data="data"
        :title="title"
      />
      <LogItem
        v-for="(item, index) in dataItems"
        :key="index"
        :data="item"
        :title="`${title} #${index + 1}`"
      />
    </TeleportSafe>
  </DevOnly>
</template>

<script lang="ts" setup>
import LogItem from './LogItem.vue'

defineProps<{
  data?: any
  dataItems?: any[]
  title?: string
}>()
</script>
