<template>
  <div
    v-if="!isHideCaption"
    class="mb-4 text-gray-500"
  >
    <span class="font-bold">ผลลัพธ์ทั้งหมด:</span>
    จำนวน
    <span class="font-bold">{{ pageOptions?.totalCount || 0 }}</span>
    รายการ
  </div>
  <UTable
    :loading="status.isLoading"
    :columns="uTableCompatibleColumns"
    :rows="rawData"
    v-bind="$attrs"
  >
    <template #loading-state>
      <div class="flex h-60 items-center justify-center">
        <Icon
          name="i-svg-spinners:180-ring-with-bg"
          class="text-primary size-8"
        />
      </div>
    </template>
    <template #empty-state>
      <Empty />
    </template>
    <template
      v-for="column in columns"
      #[`${column.accessorKey}-data`]="{ row }"
      :key="column.accessorKey"
    >
      <component
        :is="column.type === COLUMN_TYPES.COMPONENT
          ? column.component
          : (column.type ? columnTypeComponents[column.type] : undefined)"
        v-if="column.type === COLUMN_TYPES.COMPONENT || (column.type && columnTypeComponents[column.type])"
        :value="transformValue(column, row)"
        :column="column"
        :row="row"
      />
      <ColumnText
        v-else
        :value="transformValue(column, row)"
        :column="column"
        :row="row"
      />
    </template>

    <template
      v-for="(_, slotName) in $slots"
      #[slotName]="scope"
    >
      <slot
        :name="slotName"
        v-bind="scope"
      />
    </template>
  </UTable>
  <div
    v-if="pageOptions"
    class="mt-4 flex justify-between px-3"
  >
    <p class="text-sm text-gray-500">
      ผลลัพธ์ {{ pageBetween }} ของ {{ totalCountWithComma }} รายการ
    </p>
    <Pagination
      v-if="pageOptions.totalPage > 1 && !isHidePagination"
      v-model="page"
      :page-count="pageOptions.limit"
      :total="pageOptions.totalCount"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, type PropType } from 'vue'
import { COLUMN_TYPES, type ITableOptions, type TableColumn } from '#core/components/Table/types'
import ColumnNumber from '#core/components/Table/ColumnNumber.vue'
import ColumnImage from '#core/components/Table/ColumnImage.vue'
import { NumberHelper } from '#core/utils/NumberHelper'
import { ref, watch } from '#imports'
import ColumnDateTime from '#core/components/Table/ColumnDateTime.vue'
import Empty from '#core/components/Empty.vue'
import ColumnDate from '#core/components/Table/ColumnDate.vue'
import ColumnText from '#core/components/Table/ColumnText.vue'
import { useWatchChange } from '#core/composables/useWatch'
import UTable from '#ui/components/Table'

const emits = defineEmits(['pageChange'])

const props = defineProps({
  status: {
    type: Object as PropType<ITableOptions['status']>,
    required: true,
  },
  pageOptions: {
    type: Object as PropType<ITableOptions['pageOptions']>,
    required: false,
  },
  columns: {
    type: Array as PropType<ITableOptions['columns']>, // This resolves to TableColumn[]
    required: true,
  },
  rawData: {
    type: Array as PropType<ITableOptions['rawData']>,
    required: true,
  },
  isHidePagination: {
    type: Boolean as PropType<ITableOptions['isHidePagination']>,
    default: false,
  },
  isHideCaption: {
    type: Boolean as PropType<ITableOptions['isHideCaption']>,
    default: false,
  },
})

const page = ref(props.pageOptions?.currentPage || 1)

const uTableCompatibleColumns = computed(() =>
  props.columns.map((col) => ({
    ...col,
    key: col.accessorKey, // Use accessorKey for UTable's key property
  })),
)

const columnTypeComponents = {
  [COLUMN_TYPES.NUMBER]: ColumnNumber,
  [COLUMN_TYPES.IMAGE]: ColumnImage,
  [COLUMN_TYPES.DATE_TIME]: ColumnDateTime,
  [COLUMN_TYPES.DATE]: ColumnDate,
  [COLUMN_TYPES.TEXT]: ColumnText,
}

useWatchChange(
  () => props.pageOptions?.currentPage,
  (value: number) => {
    page.value = value
  },
)

const pageBetween = computed((): string => {
  const length = props.rawData?.length

  if (length === 0) {
    return '0'
  }

  const start = (props.pageOptions!.currentPage - 1) * props.pageOptions!.limit + 1
  const end = start + length - 1

  return `${start} - ${end}`
})

const transformValue = (column: TableColumn, row: any) => {
  const value = row[column.accessorKey]

  return column.transform ? column.transform(value, row, column) : value
}

const totalCountWithComma = computed((): string => {
  return !props.pageOptions?.totalCount
    ? '0'
    : NumberHelper.withComma(props.pageOptions!.totalCount)
})

watch(page, () => {
  emits('pageChange', page.value)
})
</script>
