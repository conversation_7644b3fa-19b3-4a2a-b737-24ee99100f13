<template>
  <Base
    v-bind="$attrs"
    :columns="options.columns"
    :raw-data="itemsByPage"
    :status="options.status"
    :page-options="pageOptions"
    :is-hide-pagination="options.isHidePagination"
    :is-hide-caption="options.isHideCaption"
    @page-change="onPageChange"
  >
    <template
      v-for="(_, slot) of $slots"
      #[slot]="slotProps"
    >
      <slot
        :name="slot"
        v-bind="slotProps || {}"
      />
    </template>
  </Base>
</template>

<script lang="ts" setup>
import { computed, type PropType, ref } from 'vue'
import type { ISimpleTableOptions } from '#core/components/Table/types'
import Base from '#core/components/Table/Base.vue'
import { initPageOptions } from '#core/composables/loaderPage'
import { useCoreConfig } from '#core/composables/useConfig'

defineSlots<{
  'empty-state': () => any
  'loading-state': () => any
}>()

const props = defineProps({
  options: {
    type: Object as PropType<ISimpleTableOptions>,
    required: true,
  },
})

const currentPage = ref(1)
const coreConfig = useCoreConfig()

const pageOptions = computed(() => {
  if (!props.options?.limit) {
    return undefined
  }

  const totalCount = props.options.rawData.length
  const limit = props.options.limit

  return {
    ...initPageOptions({
      limit: limit,
      primary: coreConfig.default_primary_key,
    }),
    totalCount: totalCount,
    totalPage: Math.ceil(totalCount / limit),
    currentPage: currentPage.value,
  }
})

const onPageChange = (page: number) => {
  currentPage.value = page
}

const itemsByPage = computed(() => {
  if (!pageOptions.value) {
    return props.options?.rawData
  }

  const start = (pageOptions.value.currentPage - 1) * pageOptions.value.limit
  const end = start + pageOptions.value.limit

  return props.options?.rawData.slice(start, end)
})
</script>
