<template>
  <div ref="tableContainer">
    <div
      v-if="options.isEnabledSearch"
      :class="theme.searchContainer({
        class: [ui?.searchContainer],
      })"
    >
      <Input
        v-model="q"
        icon="i-heroicons-magnifying-glass"
        :placeholder="options.searchPlaceholder || 'ค้นหา....'"
      />
    </div>
    <UTable
      v-bind="$attrs"
      :loading="options.status.isLoading"
      :data="options.rawData"
      :columns="options.columns"
      :ui="ui"
    >
      <template #empty>
        <slot
          v-if="options.status.isLoading"
          name="loading"
        >
          <Loader
            :loading="true"
          />
        </slot>
        <slot
          v-else-if="options.status.isError"
          name="error"
        >
          <div
            class="
              text-error-400 flex h-[200px] items-center justify-center text-2xl
            "
          >
            {{ StringHelper.getError(options.status.errorData) }}
          </div>
        </slot>

        <slot
          v-else
          name="error"
        >
          <Empty />
        </slot>
      </template>
      <template
        v-for="column in options.columns.filter((item) => !!item.type)"
        #[`${column.accessorKey}-cell`]="{ row }"
        :key="column.accessorKey"
      >
        <component
          :is="column.type === COLUMN_TYPES.COMPONENT ? column.component : columnTypeComponents[column.type!]"
          v-if="column.type === COLUMN_TYPES.COMPONENT || columnTypeComponents[column.type!]"
          :value="transformValue(column, row)"
          :column="column"
          :row="row"
        />
      </template>
      <template
        v-for="(_, slotName) of $slots"
        #[slotName]="slotProps"
      >
        <slot
          :name="slotName"
          v-bind="slotProps || {}"
        />
      </template>
    </UTable>

    <div
      v-if="!options.isHidePagination"
      :class="theme.paginationContainer({
        class: [ui?.paginationContainer],
      })"
    >
      <p
        :class="theme.paginationInfo({
          class: [ui?.paginationInfo],
        })"
      >
        {{ pageBetween }} รายการ จากทั้งหมด {{ totalCountWithComma }} รายการ
      </p>
      <Pagination
        v-if="options.pageOptions.totalPage > 1 "
        v-model:page="page"
        :default-page="options.pageOptions?.currentPage || 1"
        :items-per-page="options.pageOptions.limit"
        :total="options.pageOptions.totalCount"
        :to="options.isRouteChange? to : undefined"
        @update:page="onPageChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { ButtonProps, TableRow, TableSlots } from '@nuxt/ui'
import type { TableColumn, ITableOptions } from '#core/components/Table/types'
import { COLUMN_TYPES } from '#core/components/Table/types'
import { _debounce, ref, useUiConfig, watch, useWatchChange, useRouter } from '#imports'
import { NumberHelper } from '#core/utils/NumberHelper'
import ColumnDate from '#core/components/Table/ColumnDate.vue'
import ColumnDateTime from '#core/components/Table/ColumnDateTime.vue'
import ColumnImage from '#core/components/Table/ColumnImage.vue'
import ColumnText from '#core/components/Table/ColumnText.vue'
import ColumnNumber from '#core/components/Table/ColumnNumber.vue'
import { tableTheme } from '#core/theme/table'
import UTable from '#ui/components/Table'

const columnTypeComponents = {
  [COLUMN_TYPES.NUMBER]: ColumnNumber,
  [COLUMN_TYPES.IMAGE]: ColumnImage,
  [COLUMN_TYPES.DATE_TIME]: ColumnDateTime,
  [COLUMN_TYPES.DATE]: ColumnDate,
  [COLUMN_TYPES.TEXT]: ColumnText,
}

type Slot = TableSlots<any> & {
  error: (props?: Record<string, any>) => any
}

defineSlots<Slot>()

const emits = defineEmits<{
  (event: 'pageChange', page: number): void
  (event: 'search', q: string): void
}>()

const props = defineProps<{
  options: ITableOptions<any>
  ui?: typeof tableTheme['slots']
}>()

const page = ref(props.options?.pageOptions?.currentPage || 1)

const tableContainer = ref<HTMLElement>()
const router = useRouter()

const theme = computed(() => useUiConfig(tableTheme, 'table')())

const q = ref(props.options?.pageOptions.search ?? '')

const to = (page: number): ButtonProps['to'] => {
  const params = props.options?.pageOptions?.request?.params || {}

  // Filter out empty values
  const cleanParams = Object.fromEntries(
    Object.entries(params).filter(([key, value]) =>
      value !== null && value !== undefined && value !== '',
    ),
  )

  return {
    query: {
      ...cleanParams,
      page,
    },
  }
}

watch(
  q,
  _debounce((value) => {
    emits('search', value)
  }, 500),
)

useWatchChange(() => props.options?.pageOptions?.currentPage, (value: number) => {
  page.value = value
})

const pageBetween = computed((): string => {
  const rawDataLength = props.options.rawData?.length
  const pageOpts = props.options.pageOptions

  if (!rawDataLength || !pageOpts) {
    return '0'
  }

  const currentPage = pageOpts.currentPage || 1
  const limit = pageOpts.limit

  if (limit <= 0) {
    return '0'
  }

  const start = (currentPage - 1) * limit + 1
  const end = start + rawDataLength - 1

  return `${start} - ${end}`
})

const totalCountWithComma = computed((): string => {
  const total = props.options.pageOptions?.totalCount

  if (!total || total <= 0) {
    return '0'
  }

  return NumberHelper.withComma(total)
})

const transformValue = (column: TableColumn, row: TableRow<any>) => {
  return column.cell
    ? column.cell({
      row,
    })
    : row.getValue(column.accessorKey)
}

const onPageChange = (newPage: number) => {
  // scroll to top of the table when page changes
  if (tableContainer.value) {
    const rect = tableContainer.value.getBoundingClientRect()
    const scrollTop = window.pageYOffset + rect.top - 100

    window.scrollTo({
      top: scrollTop,
      behavior: 'smooth',
    })
  }

  emits('pageChange', newPage)
}
</script>
