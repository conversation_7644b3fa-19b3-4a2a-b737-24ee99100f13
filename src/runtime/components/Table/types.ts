import type { Component } from 'vue'
import type { TableColumn as NuxtUiTableColumn } from '@nuxt/ui'
import type { IPageOptions, IStatus } from '#core/types/lib'

export enum COLUMN_TYPES {
  COMPONENT = 'COMPONENT',
  DATE = 'DATE',
  DATE_TIME = 'DATE_TIME',
  NUMBER = 'NUMBER',
  IMAGE = 'IMAGE',
  TEXT = 'TEXT',
}

export type TableColumn<T extends Record<string, any> = Record<string, any>> = NuxtUiTableColumn<T> & {
  type?: COLUMN_TYPES
  component?: Component
  accessorKey: string
  meta?: NuxtUiTableColumn<T>['meta'] & Record<string, any>
}

export interface IBaseTableOptions<T extends Record<string, any> = Record<string, any>> {
  rawData: T[]
  status: IStatus
  columns: TableColumn<T>[]
  isHidePagination?: boolean
  isHideCaption?: boolean
}

export interface ITableOptions<T extends Record<string, any> = Record<string, any>> extends IBaseTableOptions<T> {
  pageOptions: IPageOptions
  isHideToolbar?: boolean
  isEnabledSearch?: boolean
  searchPlaceholder?: string
  isRouteChange: boolean
}

export interface ISimpleTableOptions<T extends Record<string, any> = Record<string, any>> extends IBaseTableOptions<T> {
  limit?: number
  primary?: number
}
