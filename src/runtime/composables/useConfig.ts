import { tv } from 'tailwind-variants'
import type { core as Core } from '../../core.config'
import appConfig from '#build/app.config'

export const useCoreConfig = (): typeof Core => {
  return appConfig.core
}

export const useUiConfig = (config: object, name: string): any => {
  return tv({
    extend: tv(config as any),
    ...(appConfig.ui[name] || {}),
  })
}

export const useUiIconConfig = (name: string): any => {
  return appConfig.ui[name].icons
}

export const useUiStaticConfig = (name: string): any => {
  return appConfig.ui[name].slots
}
