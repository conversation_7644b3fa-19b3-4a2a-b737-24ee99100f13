import type { ComputedRef } from 'vue'
import type { Store } from 'pinia'
import { get } from '@vueuse/core'
import { computed, useCoreConfig } from '#imports'
import type { IUsePageLoader } from '#core/helpers/apiPageHelper'
import type { IFlexDeckOptions } from '#core/components/FlexDeck/types'

export interface IUseFlexDeck<T = object> {
  repo: IUsePageLoader<T> | Store<any, any>
  options?: (() => Partial<IFlexDeckOptions<T>>) | Partial<IFlexDeckOptions<T>>
}

export const createFlexDeckOptions = <T = object>(
  repo: IUsePageLoader<T>,
  options: Partial<IFlexDeckOptions<T>>,
): IFlexDeckOptions<T> => {
  const config = useCoreConfig()

  return {
    rawData: get(repo.fetch.items) as T[],
    pageOptions: get(repo.fetch.options),
    status: get(repo.fetch.status),
    primary: get(repo.fetch.options).primary || config.default_primary_key!,
    isRouteChange: false,
    ...options,
  }
}

export const useFlexDeck = <T = object>(
  options: IUseFlexDeck<T>,
): ComputedRef<IFlexDeckOptions<T>> =>
  computed<IFlexDeckOptions<T>>(() => {
    return createFlexDeckOptions<T>(
      options.repo,
      typeof options.options === 'function'
        ? options.options()
        : (options.options ?? {}),
    )
  })
