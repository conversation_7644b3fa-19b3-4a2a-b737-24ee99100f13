import type { ComputedRef } from 'vue'
import { type FieldContext, type FieldOptions, useField } from 'vee-validate'

import { toTypedSchema } from '@vee-validate/valibot'
import type { IFieldProps, IFormField } from '../components/Form/types'
import { computed, toRef } from '#imports'

interface IFieldContext<TValue> extends FieldContext<TValue> {
  wrapperProps: ComputedRef<IFieldProps>
}

export const useFieldHOC = <TValue = unknown> (
  newFormProps: IFieldProps,
  opts?: Partial<FieldOptions<TValue>>,
): IFieldContext<TValue> => {
  const field = useField(toRef(newFormProps, 'name'),
    toTypedSchema(newFormProps.rules), {
      form: newFormProps.form,
      ...opts,
    })

  return {
    ...field,
    wrapperProps: computed<IFieldProps>(() => ({
      ...newFormProps,
      ui: newFormProps.containerUi || {},
      placeholder: newFormProps.placeholder || newFormProps.label,
      errorMessage: field.errorMessage.value,
    })),
  }
}

export const createFormFields = (fields: () => IFormField[]): ComputedRef<IFormField[]> =>
  computed<IFormField[]>(fields)

export const moveToError = ({
  errors,
}) => {
  const firstErrorFieldName = Object.keys(errors)[0]
  const el: HTMLElement | null = document.querySelector(`[name="${firstErrorFieldName}"]`)

  if (el) {
    // Focus the element, preventing the default scroll-on-focus behavior
    el.focus({
      preventScroll: true,
    })

    // Calculate the target scroll position
    const elementRect = el.getBoundingClientRect()
    const absoluteElementTop = elementRect.top + window.scrollY
    const paddingTop = 60 // Desired padding from the top in pixels

    window.scrollTo({
      top: absoluteElementTop - paddingTop,
      behavior: 'smooth',
    })
  }
}
