import type { AxiosRequestConfig } from 'axios'
import { useObjectLoader } from './loaderObject'

export interface IUploadRequest {
  requestOptions: Omit<AxiosRequestConfig, 'baseURL'> & { baseURL: string }
  pathURL?: string
}

export const useUploadLoader = (request: IUploadRequest) => {
  return useObjectLoader<any>({
    method: 'post',
    url: request.pathURL || '',
    getRequestOptions: () => ({
      ...request.requestOptions,
      headers: {
        ...request.requestOptions.headers,
        'Content-Type': 'multipart/form-data',
      },
    }),
  })
}
