import type { AxiosRequestConfig, Method } from 'axios'
import type { IAPIOptions, IStatus } from '../types/lib'
import { ObjectHelper } from '../utils/ObjectHelper'
import { NewRequester } from '../lib/Requester'
import { _merge } from '#imports'

export interface IBaseLoaderOptions {
  isMock?: boolean
  params?: Record<string, any>
  expire?: number

  [key: string]: any
}

export interface IRequestContext {
  url: string
  method: string
  data?: any
  options: AxiosRequestConfig
}

export const checkExpiration = (
  timestamp: number | undefined, expire?: number): boolean => {
  if (!expire || !timestamp) return false

  return timestamp + expire > Date.now()
}

export const bindParamsToUrl = (urlTemplate: string, urlParams: Record<string, any> = {}): string => {
  let resultUrl = urlTemplate

  Object.entries(urlParams).forEach(([key, value]) => {
    resultUrl = resultUrl.replace(`:${key}`, String(value))
  })

  return resultUrl
}

export const prepareRequestOptions = (
  baseOptions?: AxiosRequestConfig,
  requestOptions?: AxiosRequestConfig,
  params?: Record<string, any>,
): AxiosRequestConfig => {
  const options = _merge({}, baseOptions, requestOptions, {
    params,
  }) as AxiosRequestConfig

  return options
}

export const handleApiRequest = async <T> (
  requestContext: IRequestContext,
  mockData: T | T[] | null,
  callbacks: {
    onSuccess: (data: T, status?: number) => void
    onError: (error: any) => void
    onComplete?: () => void
  },
) => {
  const {
    url, method, data, options,
  } = requestContext

  try {
    if (mockData) {
      // For mock data, simulate a delay and return the mock data
      await new Promise((resolve) => setTimeout(resolve, 300))
      callbacks.onSuccess(mockData as T)
    } else {
      // Execute the actual API request
      const response = await NewRequester.create<T>(method as Method, url, data, options)

      callbacks.onSuccess(response.data, response.status)
    }
  } catch (error: any) {
    callbacks.onError(error)
  } finally {
    callbacks.onComplete?.()
  }
}

export const updateLoaderState = {
  setLoading: (status: IStatus): IStatus => ObjectHelper.toLoadingStatus(
    status),
  setSuccess: (status: IStatus): IStatus => ObjectHelper.toSuccessStatus(
    status),
  setError: (
    status: IStatus, error: any): IStatus => ObjectHelper.toErrorStatus(status,
    error),
  setComplete: (status: IStatus): IStatus => ObjectHelper.toCompleteStatus(
    status),
  setOptions: (
    options: IAPIOptions, updates: Partial<IAPIOptions>): IAPIOptions => ({
    ...options,
    ...updates,
    _timestamp: Date.now(),
  }),
}
