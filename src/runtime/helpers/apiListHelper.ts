import type { AxiosRequestConfig } from 'axios'
import type { IAPIListState, IAPIOptions, IStatus } from '../types/lib'
import { bindParamsToUrl, type IBaseLoaderOptions } from './apiBaseHelper'
import {
  checkExpiration,
  prepareRequestOptions,
  handleApiRequest,
  updateLoaderState,
} from './apiBaseHelper'

export interface IListRunLoaderOptions<T = any, D = Record<string, any>> extends IBaseLoaderOptions {
  mockItems?: T[]
  data?: D
}

export interface IListLoaderOptions<T = any, O = any> {
  url: string
  primary?: string
  mockItems?: T[]
  getURL?: (opts: IListRunLoaderOptions<T, O>) => string
  getRequestOptions?: (opts: IListRunLoaderOptions<T, O>) => AxiosRequestConfig
}

export const apiListHelper = async <T, O>(
  state: () => IAPIListState<T>,
  onUpdateStatus: (status: IStatus) => void,
  onUpdateOptions: (options: IAPIOptions) => void,
  onUpdateItems: (data: T[]) => void,
  opts: IListLoaderOptions<T, O> & IListRunLoaderOptions<T, O>,
): Promise<void> => {
  // Check if cache is still valid
  if (checkExpiration(state().options._timestamp, opts.expire)) {
    onUpdateStatus(updateLoaderState.setComplete(state().status))

    return
  }

  // Set loading state
  onUpdateStatus(updateLoaderState.setLoading(state().status))
  onUpdateOptions({})

  // Prepare request options
  const requestOptions = prepareRequestOptions(
    {},
    opts.getRequestOptions?.(opts),
    opts.params,
  )

  // Determine URL
  const url = bindParamsToUrl(opts.getURL ? opts.getURL(opts) : opts.url, opts.urlBind || {})

  // Handle the API request
  await handleApiRequest<T[]>(
    {
      url,
      method: 'GET',
      options: requestOptions,
    },
    opts.isMock ? opts.mockItems || [] : null,
    {
      onSuccess: (items, status) => {
        onUpdateItems(items)
        onUpdateStatus(updateLoaderState.setSuccess(state().status))
        onUpdateOptions({
          _timestamp: Date.now(),
          _status: status,
          request: requestOptions,
        })
      },
      onError: (error) => {
        onUpdateStatus(updateLoaderState.setError(state().status, error))
        onUpdateOptions({
          _status: error.response?.status,
          request: requestOptions,
        })
      },
      onComplete: () => {
        onUpdateStatus(updateLoaderState.setComplete(state().status))
      },
    },
  )
}
