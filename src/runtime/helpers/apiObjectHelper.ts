import type { AxiosRequestConfig, Method } from 'axios'
import type { ComputedRef } from 'vue'
import type { IAPIObjectState, IAPIOptions, IStatus } from '../types/lib'
import { bindParamsToUrl, type IBaseLoaderOptions } from './apiBaseHelper'
import {
  checkExpiration,
  handleApiRequest,
  prepareRequestOptions,
  updateLoaderState,
} from './apiBaseHelper'
import type { IListRunLoaderOptions } from './apiListHelper'

export interface IObjectRunLoaderOptions<T = any, B = any> extends IBaseLoaderOptions {
  mockItem?: T
  data?: B
  urlBind?: Record<string, any>
}

export interface IObjectLoaderOptions<T = any, B = any, _O = any> {
  url: string
  method: Method
  primary?: string
  mockItem?: T
  getURL?: (data: B | undefined, opts: IObjectRunLoaderOptions<T, B>) => string
  getRequestOptions?: (
    data: B | undefined,
    opts: IObjectRunLoaderOptions<T, B>,
  ) => AxiosRequestConfig
}

export interface IUseObjectLoader<T, B, _O> {
  status: ComputedRef<IStatus>
  data: ComputedRef<T | null>
  options: ComputedRef<IAPIOptions>
  run: (payload?: IObjectRunLoaderOptions<T, B>) => Promise<void>
  clear: () => void
  setLoading: () => void
  setData: (data: T | null) => void
}

export interface IUseListLoader<T, O> {
  status: ComputedRef<IStatus>
  items: ComputedRef<T[]>
  options: ComputedRef<Record<string, any>>
  run: (opts?: IListRunLoaderOptions<T, O>) => void
  setItems: (items: T[]) => void
  clear: () => void
  setLoading: () => void
}

export const apiObjectHelper = async <T, B, _O> (
  state: () => IAPIObjectState<T>,
  onUpdateStatus: (status: IStatus) => void,
  onUpdateOptions: (options: IAPIOptions) => void,
  onUpdateData: (data?: T) => void,
  payload: B | undefined,
  opts: IObjectLoaderOptions<T, B, _O> & IObjectRunLoaderOptions<T, B>,
): Promise<void> => {
  // Check if cache is still valid
  if (checkExpiration(state().options._timestamp, opts.expire)) {
    onUpdateStatus(updateLoaderState.setComplete(state().status))

    return
  }

  // Set loading state
  onUpdateStatus(updateLoaderState.setLoading(state().status))
  onUpdateOptions({})

  // Prepare request options
  const requestOptions = prepareRequestOptions(
    {},
    opts.getRequestOptions?.(payload, opts),
    opts.params,
  )

  // Determine URL
  const url = bindParamsToUrl(opts.getURL ? opts.getURL(payload, opts) : opts.url, opts.urlBind || {})

  // Handle the API request
  await handleApiRequest<T>(
    {
      url,
      method: opts.method,
      data: payload,
      options: requestOptions,
    },
    opts.isMock ? opts.mockItem || null : null,
    {
      onSuccess: (item, status) => {
        onUpdateData(item)
        onUpdateStatus(updateLoaderState.setSuccess(state().status))
        onUpdateOptions({
          request: requestOptions,
          _timestamp: Date.now(),
          _status: status,
        })
      },
      onError: (error) => {
        onUpdateStatus(updateLoaderState.setError(state().status, error))
        onUpdateOptions({
          request: requestOptions,
          _status: error.response?.status,
        })
      },
      onComplete: () => {
        onUpdateStatus(updateLoaderState.setComplete(state().status))
      },
    },
  )
}
