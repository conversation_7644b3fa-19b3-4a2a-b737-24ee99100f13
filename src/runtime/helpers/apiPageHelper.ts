import type { AxiosRequestConfig } from 'axios'

import type {
  IAPIAddState,
  IAPIDeleteState,
  IAPIFetchState,
  IAPIFindState,
  IAPIOptions,
  IAPIUpdateState,
  IPageOptions,
  IStatus,
} from '../types/lib'
import { useCoreConfig } from '../composables/useConfig'
import { _get } from '../utils/lodash'
import { bindParamsToUrl, type IBaseLoaderOptions } from './apiBaseHelper'
import {
  checkExpiration,
  handleApiRequest,
  prepareRequestOptions,
  updateLoaderState,
} from './apiBaseHelper'
import { _findIndex, _shuffle } from '#imports'

export interface IPageFetchLoaderOptions<D = Record<string, any>> extends IBaseLoaderOptions {
  data?: D
}

export interface IPageFindLoaderOptions<D = Record<string, any>> extends IBaseLoaderOptions {
  data?: D
}

export interface IPageUpdateLoaderOptions<D = Record<string, any>> extends IBaseLoaderOptions {
  data?: D
}

export interface IPageDeleteLoaderOptions<D = Record<string, any>> extends IBaseLoaderOptions {
  data?: D
}

export interface IPageAddLoaderOptions<D = Record<string, any>> extends IBaseLoaderOptions {
  data?: D
}

export interface IPageLoaderOptions<T> {
  baseURL: string
  primary?: string
  isMock?: boolean
  mockItems?: T[]
  getBaseRequestOptions?: () => AxiosRequestConfig
  fetch?: {
    getURL?: (
      page: number, query: string, opts: IPageFetchLoaderOptions<any>) => string
    getRequestOptions?: (
      page: number,
      query: string,
      opts: IPageFetchLoaderOptions<any>,
    ) => AxiosRequestConfig
  }
  find?: {
    getURL?: (id: string | number, opts: IPageFindLoaderOptions<any>) => string
    getRequestOptions?: (
      id: string | number,
      opts: IPageFindLoaderOptions<any>,
    ) => AxiosRequestConfig
  }
  update?: {
    getURL?: (
      id: string | number, data: any,
      opts: IPageUpdateLoaderOptions<any>) => string
    getRequestOptions?: (
      id: string | number,
      data: any,
      opts: IPageUpdateLoaderOptions<any>,
    ) => AxiosRequestConfig
  }
  delete?: {
    getURL?: (
      id: string | number, opts: IPageDeleteLoaderOptions<any>) => string
    getRequestOptions?: (
      id: string | number,
      opts: IPageDeleteLoaderOptions<any>,
    ) => AxiosRequestConfig
  }
  add?: {
    getURL?: (data: any, opts: IPageAddLoaderOptions<any>) => string
    getRequestOptions?: (
      data: any, opts: IPageAddLoaderOptions<any>) => AxiosRequestConfig
  }
}

export interface IUsePageLoader<T> {
  fetch: {
    status: IStatus
    items: T[]
    options: IPageOptions

  }
  find: {
    status: IStatus
    item: T | null
    options: IAPIOptions
    value: (id: string, opts?: any) => Promise<void>
  }
  add: {
    status: IStatus
    item: T | null
    options: IAPIOptions
  }
  update: {
    status: IStatus
    item: T | null
    options: IAPIOptions
  }
  delete: {
    status: IStatus
    item: T | null
    options: IAPIOptions
  }
  fetchPage: (page?: number, query?: string, opts?: IPageFetchLoaderOptions) => Promise<void>
  fetchPageChange: (page: number, opts?: IPageFetchLoaderOptions) => Promise<void>
  fetchSearch: (query: string, opts?: IPageFetchLoaderOptions) => Promise<void>
  fetchSetLoading: () => void
  findSetLoading: () => void
  findRun: (id: string, opts?: IPageFindLoaderOptions) => Promise<void>
  addRun: (payload: IPageAddLoaderOptions<Partial<T> | Record<string, any>>) => Promise<void>
  updateRun: (id: string, payload: IPageUpdateLoaderOptions<Partial<T> | Record<string, any>>) => Promise<void>
  deleteRun: (id: string, opts?: IPageDeleteLoaderOptions) => Promise<void>
  clearAll: () => void
}

export const apiAddHelper = async <T> (
  state: () => IAPIAddState<T>,
  onUpdateStatus: (status: IStatus) => void,
  onUpdateOptions: (options: IAPIOptions) => void,
  onUpdateData: (data: any) => void,
  onUpdateItems: (data: any[]) => void,
  data: any,
  opts: IPageLoaderOptions<any> & IPageFetchLoaderOptions,
) => {
  // Set loading state
  onUpdateStatus(updateLoaderState.setLoading(state().status))
  onUpdateOptions({})

  // Prepare request options
  const requestOptions = prepareRequestOptions(
    opts.getBaseRequestOptions?.() || {},
    opts.add?.getRequestOptions?.(data, opts),
    opts.params,
  )

  // Determine URL
  const url = bindParamsToUrl(opts.add?.getURL
    ? opts.add.getURL(data, opts)
    : opts.baseURL, opts.pathBind)

  // Handle the API request
  await handleApiRequest<T>(
    {
      url,
      method: 'POST',
      data,
      options: requestOptions,
    },
    opts.isMock ? opts.mockItems?.[0] || null : null,
    {
      onSuccess: (item, status) => {
        onUpdateData(item)

        // Update items array if it exists
        if (state().items) {
          onUpdateItems([item, ...state().items])
        }

        onUpdateStatus(updateLoaderState.setSuccess(state().status))
        onUpdateOptions({
          _timestamp: Date.now(),
          _status: status,
          request: requestOptions,
        })
      },
      onError: (error) => {
        onUpdateStatus(updateLoaderState.setError(state().status, error))
        onUpdateOptions({
          _status: error.response?.status,
          request: requestOptions,
        })
      },
      onComplete: () => {
        onUpdateStatus(updateLoaderState.setComplete(state().status))
      },
    },
  )
}

export const apiDeleteHelper = async <T> (
  state: () => IAPIDeleteState<T>,
  onUpdateStatus: (status: IStatus) => void,
  onUpdateOptions: (options: IAPIOptions) => void,
  _onUpdateData: (data: T) => void,
  onUpdateItems: (data: T[]) => void,
  id: string | number,
  opts: IPageLoaderOptions<any> & IPageDeleteLoaderOptions,
) => {
  const primaryKey = opts.primary ?? 'id'
  const getPrimaryKey = (item: any): string => _get(item, primaryKey, '')

  // Set loading state
  onUpdateStatus(updateLoaderState.setLoading(state().status))
  onUpdateOptions({})

  // Prepare request options
  const requestOptions = prepareRequestOptions(
    opts.getBaseRequestOptions?.() || {},
    opts.delete?.getRequestOptions?.(id, opts),
    opts.params,
  )

  // Determine URL
  const url = bindParamsToUrl(opts.delete?.getURL
    ? opts.delete.getURL(id, opts)
    : `${opts.baseURL}/${id}`, opts.pathBind)

  // Handle the API request
  await handleApiRequest<T | undefined>(
    {
      url,
      method: 'DELETE',
      options: requestOptions,
    },
    opts.isMock ? null : null, // No mock data needed for delete
    {
      onSuccess: (_, status) => {
        // Update items array if it exists
        if (state().items) {
          onUpdateItems(
            state().items.filter((item) => getPrimaryKey(item) !== id),
          )
        }

        onUpdateStatus(updateLoaderState.setSuccess(state().status))
        onUpdateOptions({
          _timestamp: Date.now(),
          _status: status,
          request: requestOptions,
        })
      },
      onError: (error) => {
        onUpdateStatus(updateLoaderState.setError(state().status, error))
        onUpdateOptions({
          _status: error.response?.status,
          request: requestOptions,
        })
      },
      onComplete: () => {
        onUpdateStatus(updateLoaderState.setComplete(state().status))
      },
    },
  )
}

export const apiFetchHelper = async <T> (
  state: () => IAPIFetchState<T>,
  onUpdateStatus: (status: IStatus) => void,
  onUpdateOptions: (options: IPageOptions) => void,
  onUpdateItems: (items: T[]) => void,
  page: number,
  query: string,
  opts: IPageLoaderOptions<T> & IPageFetchLoaderOptions<any>,
) => {
  const config = useCoreConfig()

  // Check if cache is still valid
  if (checkExpiration(state().options._timestamp, opts.expire)) {
    onUpdateStatus(updateLoaderState.setComplete(state().status))

    return
  }

  // Set loading state and initial options
  onUpdateStatus(updateLoaderState.setLoading(state().status))
  onUpdateOptions({
    ...state().options,
    ...opts,
    currentPage: page,
    search: query,
    primary: opts.primary ?? 'id',
    _status: undefined,
  })

  const getOptions = (data: any) => {
    const {
      page, total, limit, ...more
    } = data

    return {
      ...state().options,
      ...more,
      currentPageCount: state().items?.length || 0,
      currentPage: page,
      totalCount: total,
      limit,
      search: query,
      totalPage: Math.ceil(total / data.limit),
    }
  }

  // Prepare base request options
  const baseRequestOptions = {
    ...(opts.getBaseRequestOptions?.() || {}),
    ...(opts.fetch?.getRequestOptions?.(page, query, opts) || {}),
  }

  const limit = Number(baseRequestOptions.params?.limit || config.limit_per_page)

  // Add pagination and search parameters
  const requestOptions = prepareRequestOptions(
    baseRequestOptions,
    {
      params: {
        limit,
        q: (query || '').trim(),
        page: Number(page),
      },
    },
    opts.params,
  )

  // Determine URL
  const url = bindParamsToUrl(opts.fetch?.getURL
    ? opts.fetch.getURL(page, query, opts)
    : opts.baseURL, opts.pathBind)

  // Handle the API request
  await handleApiRequest<any>(
    {
      url,
      method: 'GET',
      options: requestOptions,
    },
    opts.isMock
      ? {
        items: _shuffle(opts.mockItems || []),
        totalCount: (opts.mockItems || []).length,
        limit: limit,
      }
      : null,
    {
      onSuccess: (response, status) => {
        const {
          items, ...moreOptions
        } = response

        onUpdateItems(items)
        onUpdateOptions({
          ...getOptions(moreOptions),
          _timestamp: Date.now(),
          _status: status,
          request: requestOptions,
        })

        onUpdateStatus(updateLoaderState.setSuccess(state().status))
      },
      onError: (error) => {
        onUpdateStatus(updateLoaderState.setError(state().status, error))
        onUpdateOptions({
          ...state().options,
          _status: error.response?.status,
          request: requestOptions,
        })
      },
      onComplete: () => {
        onUpdateStatus(updateLoaderState.setComplete(state().status))
      },
    },
  )
}

export const apiFindHelper = async <T> (
  state: () => IAPIFindState<T>,
  onUpdateStatus: (status: IStatus) => void,
  onUpdateOptions: (options: IAPIOptions) => void,
  onUpdateData: (data: any) => void,
  id: string | number,
  opts: IPageLoaderOptions<T> & IPageFindLoaderOptions,
) => {
  // Check if cache is still valid
  if (checkExpiration(state().options._timestamp, opts.expire)) {
    onUpdateStatus(updateLoaderState.setComplete(state().status))

    return
  }

  // Set loading state
  onUpdateStatus(updateLoaderState.setLoading(state().status))
  onUpdateOptions({})

  // Prepare request options
  const requestOptions = prepareRequestOptions(
    opts.getBaseRequestOptions?.() || {},
    opts.find?.getRequestOptions?.(id, opts),
    opts.params,
  )

  // Determine URL
  const url = bindParamsToUrl(opts.find?.getURL
    ? opts.find.getURL(id, opts)
    : `${opts.baseURL}/${id}`, opts.pathBind)

  // Handle the API request
  await handleApiRequest<T>(
    {
      url,
      method: 'GET',
      options: requestOptions,
    },
    opts.isMock ? opts.mockItems?.[0] || null : null,
    {
      onSuccess: (item, status) => {
        onUpdateData(item)
        onUpdateStatus(updateLoaderState.setSuccess(state().status))
        onUpdateOptions({
          _timestamp: Date.now(),
          _status: status,
          request: requestOptions,
        })
      },
      onError: (error) => {
        onUpdateStatus(updateLoaderState.setError(state().status, error))
        onUpdateOptions({
          _status: error.response?.status,
          request: requestOptions,
        })
      },
      onComplete: () => {
        onUpdateStatus(updateLoaderState.setComplete(state().status))
      },
    },
  )
}

export const updateHelper = async <T> (
  state: () => IAPIUpdateState<T>,
  onUpdateStatus: (status: IStatus) => void,
  onUpdateOptions: (options: IAPIOptions) => void,
  onUpdateData: (data: T) => void,
  onUpdateItems: (data: T[]) => void,
  onUpdateOldData: (data: T) => void,
  id: string | number,
  data: any,
  opts: IPageLoaderOptions<any> & IPageUpdateLoaderOptions,
) => {
  // Check if cache is still valid
  if (checkExpiration(state().options._timestamp, opts.expire)) {
    onUpdateStatus(updateLoaderState.setComplete(state().status))

    return
  }

  // Set loading state
  onUpdateStatus(updateLoaderState.setLoading(state().status))
  onUpdateOptions({})

  // Prepare request options
  const requestOptions = prepareRequestOptions(
    opts.getBaseRequestOptions?.() || {},
    opts.update?.getRequestOptions?.(id, data, opts),
    opts.params,
  )

  // Determine URL
  const url = bindParamsToUrl(opts.update?.getURL
    ? opts.update.getURL(id, data, opts)
    : `${opts.baseURL}/${id}`, opts.pathBind)

  // Handle the API request
  await handleApiRequest<T>(
    {
      url,
      method: 'PUT',
      data,
      options: requestOptions,
    },
    opts.isMock ? opts.mockItems?.[0] || null : null,
    {
      onSuccess: (item, status) => {
        onUpdateData(item)
        const finalItem = item

        // Update oldData if it exists
        if (state().oldData) {
          onUpdateOldData(finalItem)
        }

        // Update item in items array if it exists
        const currentItems = state().items

        if (currentItems) {
          const index = _findIndex(currentItems, (existingItem: any) => existingItem.id === id)

          if (index !== -1) {
            const itemsTemp = [...currentItems]

            itemsTemp[index] = finalItem
            onUpdateItems(itemsTemp) // Use the callback to update items
          }
        }

        onUpdateStatus(updateLoaderState.setSuccess(state().status))
        onUpdateOptions({
          _timestamp: Date.now(),
          _status: status,
          request: requestOptions,
        })
      },
      onError: (error) => {
        onUpdateStatus(updateLoaderState.setError(state().status, error))
        onUpdateOptions({
          _status: error.response?.status,
          request: requestOptions,
        })
      },
      onComplete: () => {
        onUpdateStatus(updateLoaderState.setComplete(state().status))
      },
    },
  )
}
