export const valibotLanguageTH = {
  base64: () => 'ข้อมูล base64 ไม่ถูกต้อง',
  bic: () => 'รหัส bic ไม่ถูกต้อง',
  bytes: (issue) => `ข้อมูลต้องมีขนาด ${issue.expected} ไบต์ แต่ได้รับ ${issue.received}`,
  check: () => 'ข้อมูลที่กรอกไม่ถูกต้อง',
  credit_card: () => 'หมายเลขบัตรเครดิตไม่ถูกต้อง',
  cuid: () => 'cuid ไม่ถูกต้อง',
  cuid2: () => 'cuid2 ไม่ถูกต้อง',
  decimal: () => 'กรุณากรอกเป็นเลขทศนิยม',
  digits: () => 'กรุณากรอกเฉพาะตัวเลข',
  email: () => 'กรุณากรอกอีเมลที่ถูกต้อง',
  emoji: () => 'กรุณากรอกอีโมจิที่ถูกต้อง',
  empty: () => 'กรุณาอย่าปล่อยว่าง',
  ends_with: (issue) => `ข้อมูลต้องลงท้ายด้วย "${issue.expected}"`,
  every_item: () => 'รายการบางอย่างไม่ถูกต้อง',
  excludes: (issue) => `ข้อมูลต้องไม่ประกอบด้วย "${issue.expected}"`,
  finite: () => 'กรุณากรอกค่าที่จำกัด',
  graphemes: (issue) => `ข้อมูลต้องมี ${issue.expected} ตัวอักษร`,
  hash: () => 'ค่า hash ไม่ถูกต้อง',
  hex_color: () => 'กรุณากรอกสีในรูปแบบ hex',
  hexadecimal: () => 'กรุณากรอกเลขฐานสิบหก',
  imei: () => 'imei ไม่ถูกต้อง',
  includes: (issue) => `ข้อมูลต้องประกอบด้วย "${issue.expected}"`,
  integer: () => 'กรุณากรอกตัวเลขจำนวนเต็ม',
  ip: () => 'ที่อยู่ ip ไม่ถูกต้อง',
  ipv4: () => 'ที่อยู่ ipv4 ไม่ถูกต้อง',
  ipv6: () => 'ที่อยู่ ipv6 ไม่ถูกต้อง',
  iso_date: () => 'วันที่ในรูปแบบ iso ไม่ถูกต้อง',
  iso_date_time: () => 'วันที่และเวลาในรูปแบบ iso ไม่ถูกต้อง',
  length: (issue) => `ข้อมูลต้องมีความยาว ${issue.expected} ตัว`,
  mac: () => 'ที่อยู่ mac ไม่ถูกต้อง',
  mac_48: () => 'mac 48-bit ไม่ถูกต้อง',
  mac_64: () => 'mac 64-bit ไม่ถูกต้อง',
  max_bytes: (issue) => `ข้อมูลต้องไม่เกิน ${issue.expected} ไบต์`,
  max_graphemes: (issue) => `ตัวอักษรต้องไม่เกิน ${issue.expected} ตัว`,
  max_length: (issue) => `ความยาวต้องไม่เกิน ${issue.expected} ตัว`,
  max_size: (issue) => `ขนาดต้องไม่เกิน ${issue.expected}`,
  max_value: (issue) => `ค่าต้องไม่เกิน ${issue.expected}`,
  max_words: (issue) => `จำนวนคำต้องไม่เกิน ${issue.expected} คำ`,
  mime_type: (issue) => 'mime type ไม่ถูกต้อง',
  min_bytes: (issue) => `ข้อมูลต้องมีอย่างน้อย ${issue.expected} ไบต์`,
  min_graphemes: (issue) => `ตัวอักษรต้องมีอย่างน้อย ${issue.expected} ตัว`,
  min_length: (issue) => `ความยาวต้องมีอย่างน้อย ${issue.expected} ตัว`,
  min_size: (issue) => `ขนาดต้องไม่น้อยกว่า ${issue.expected}`,
  min_value: (issue) => {
    return `ค่าต้องมีอย่างน้อย ${issue.requirement}`
  },
  min_words: (issue) => `จำนวนคำต้องมีอย่างน้อย ${issue.expected} คำ`,
  multiple_of: (issue) => `ค่าต้องเป็นจำนวนเท่าของ ${issue.expected}`,
  nanoid: () => 'nano id ไม่ถูกต้อง',
  non_empty: () => 'กรุณากรอกข้อมูล',
  not_bytes: (issue) => `ข้อมูลต้องไม่ใช่ ${issue.expected} ไบต์`,
  not_graphemes: (issue) => `ตัวอักษรต้องไม่เท่ากับ ${issue.expected}`,
  not_length: (issue) => `ความยาวต้องไม่เท่ากับ ${issue.expected}`,
  not_size: (issue) => `ขนาดต้องไม่เท่ากับ ${issue.expected}`,
  not_value: (issue) => `ค่าต้องไม่เท่ากับ ${issue.expected}`,
  not_words: (issue) => `จำนวนคำต้องไม่เท่ากับ ${issue.expected} คำ`,
  octal: () => 'กรุณากรอกเลขฐานแปด',
  partial_check: () => 'ข้อมูลบางส่วนไม่ถูกต้อง',
  regex: () => 'รูปแบบข้อมูลไม่ถูกต้อง',
  safe_integer: () => 'กรุณากรอกตัวเลขจำนวนเต็มที่ปลอดภัย',
  size: (issue) => `ข้อมูลต้องมีขนาด ${issue.expected}`,
  some_item: () => 'รายการบางอย่างไม่ถูกต้อง',
  starts_with: (issue) => `ข้อมูลต้องเริ่มต้นด้วย "${issue.expected}"`,
  ulid: () => 'ulid ไม่ถูกต้อง',
  url: () => 'url ไม่ถูกต้อง',
  uuid: () => 'uuid ไม่ถูกต้อง',
  value: () => 'ค่าที่กรอกไม่ถูกต้อง',
  words: (issue) => `ข้อมูลต้องมีคำ ${issue.expected} คำ`,
  default: () => 'กรุณากรอกข้อมูล', // Fallback message
}
