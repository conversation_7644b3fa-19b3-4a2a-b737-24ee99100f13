export const selectMenuTheme = {
  slots: {
    base: 'w-full',
    trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200',
    selectedWrapper: 'flex w-full items-center justify-between',
    selectedLabel: 'truncate',
    clearIcon: 'size-4 cursor-pointer text-gray-300 hover:text-gray-200',
    tagsWrapper: 'flex flex-wrap gap-x-2 gap-y-1',
    tagsItem: 'px-1.5 py-0.5 rounded-sm inline-flex items-center gap-0.5 bg-primary text-white data-disabled:cursor-not-allowed data-disabled:opacity-75',
    tagsItemText: 'flex items-center gap-x-1 text-sm',
    tagsItemDelete: [
      'inline-flex items-center text-white disabled:pointer-events-none',
      'transition-colors cursor-pointer',
    ],
    tagsItemDeleteIcon: 'ph:x',
  },
  defaultVariants: {
    size: 'lg',
  },
}
