export const uploadFileDropzoneTheme = {
  icons: {
    filePreviewIcon: 'i-heroicons:document-text-solid',
    uploadIcon: 'ri:upload-cloud-2-line',
    placeholderImgIcon: 'i-material-symbols:imagesmode-outline',
    failedImgIcon: 'i-material-symbols:imagesmode-outline',
    loadingIcon: 'i-svg-spinners:180-ring-with-bg',
    actionPreviewIcon: 'ic:outline-remove-red-eye',
    actionDownloadIcon: 'material-symbols:download',
    actionDeleteIcon: 'material-symbols:delete',
    actionRetryIcon: 'stash:arrow-retry',
  },
  slots: {
    base: 'relative w-full text-base p-4 transition rounded-md flex items-center justify-center ring-1 bg-white ring-gray-300',
    wrapper: 'flex flex-col items-center w-full',
    disabled: 'bg-gray-100 border-none grayscale cursor-not-allowed',
    failed: 'border-error',
    placeholderWrapper: 'py-4 flex flex-col items-center justify-center',
    placeholder: 'text-gray-400 text-center font-light text-sm truncate',
    labelWrapper: 'flex items-center space-x-2 text-gray-400 text-center',

    labelIcon: 'size-6 text-gray-400 text-center mb-3',
    // Loading state
    onLoadingWrapper: 'flex items-center space-x-4 w-full',
    onLoadingPlaceholderWrapper: 'flex-shrink-0',
    onLoadingPlaceholderIconClass: 'size-12 text-gray-400',
    onLoadingTextWrapper: 'flex-1 min-w-0 flex items-center justify-between',
    onLoadingLoadingIconClass: 'size-10 text-primary animate-spin',
    // Preview state
    onPreviewWrapper: 'flex items-center space-x-4 rounded-md w-full',
    onPreviewImgWrapper: 'flex-shrink-0 w-16 h-16 flex justify-center items-center rounded-md overflow-hidden bg-gray-100',
    onPreviewImgClass: 'w-full h-full object-cover',
    onPreviewFileWrapper: 'flex-shrink-0 w-16 h-16 flex justify-center items-center rounded-md overflow-hidden',
    onPreviewFileClass: 'size-8 text-gray-400 m-auto',
    onPreviewTextWrapper: 'flex-1 min-w-0 flex items-center justify-between',
    // Failed state
    onFailedWrapper: 'flex items-start space-x-4 w-full rounded-md',
    onFailedFailedImgWrapper: 'flex-shrink-0',
    onFailedFailedIconClass: 'size-12',
    onFailedTextWrapper: 'flex-1 min-w-0 flex items-start justify-between',
    // Actions
    actionWrapper: 'flex items-center space-x-2',
    actionIconClass: 'size-6 text-dimmed hover:text-dimmed-600 cursor-pointer transition-colors',
    actionDeleteIconClass: 'size-6 text-(--ui-color-error-500) hover:text-(--ui-color-error-600) cursor-pointer transition-colors',
    actionRetryBtnClass: 'px-0',
  },
  variants: {
    dragover: {
      true: {
        base: 'ring-primary bg-(--ui-color-primary-50)',
      },
    },
    disabled: {
      true: {
        base: 'bg-gray-100 border-none grayscale cursor-not-allowed',
      },
    },
    failed: {
      true: {
        base: 'ring-(--ui-color-error-500) bg-(--ui-color-error-50)',
      },
    },
  },
}
