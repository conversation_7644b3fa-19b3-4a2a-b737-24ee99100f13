import { describe, it, expect } from 'vitest'
import { FileHelper } from './FileHelper'

describe('FileHelper', () => {
  describe('dataURLtoFile', () => {
    it('should convert a data URL to a File', async () => {
      const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg=='

      const filename = 'test.png'
      const file = await FileHelper.dataURLtoFile(dataUrl, filename)

      expect(file).toBeInstanceOf(File)
      expect(file.name).toBe(filename)
      expect(file.type).toBe('image/png')
    })
  })
})
