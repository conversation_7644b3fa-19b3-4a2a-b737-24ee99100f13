export class NumberHelper {
  static withComma = (value: number | string = 0): string => {
    return (+(value || 0)).toLocaleString()
  }

  static withFixed = (value: number | string = 0): string => {
    return (+(value || 0)).toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })
  }

  static toCurrency = (value: number | string = 0, currency: string = ''): string => {
    const options: Intl.NumberFormatOptions = {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }

    if (currency) {
      options.style = 'currency'
      options.currency = currency
    }

    return (+(value || 0)).toLocaleString(undefined, options)
  }

  static toNumber = (value: any): number => {
    return Number(value) || 0
  }
}
