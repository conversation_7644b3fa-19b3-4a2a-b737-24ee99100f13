import { describe, it, expect } from 'vitest'
import { ParamHelper } from './ParamHelper'

describe('ParamHelper', () => {
  describe('getParams', () => {
    it('should merge params from opts and reqOptions', () => {
      const opts = {
        params: {
          page: 1,
          limit: 10,
        },
      }

      const reqOptions = {
        params: {
          sort: 'name',
        },
      }

      const result = ParamHelper.getParams(opts, reqOptions)

      expect(result).toEqual({
        page: 1,
        limit: 10,
        sort: 'name',
      })
    })

    it('should return reqOptions.params if opts.params is not provided', () => {
      const opts = {}

      const reqOptions = {
        params: {
          sort: 'name',
        },
      }

      const result = ParamHelper.getParams(opts, reqOptions)

      expect(result).toEqual({
        sort: 'name',
      })
    })
  })

  describe('getBoolTrue', () => {
    it('should return true for truthy values', () => {
      expect(ParamHelper.getBoolTrue(true)).toBe(true)
      expect(ParamHelper.getBoolTrue(1)).toBe(true)
      expect(ParamHelper.getBoolTrue('true')).toBe(true)
    })

    it('should return false for falsy values', () => {
      expect(ParamHelper.getBoolTrue(false)).toBe(false)
      expect(ParamHelper.getBoolTrue(0)).toBe(false)
      expect(ParamHelper.getBoolTrue('false')).toBe(false)
    })

    it('should return true for null value', () => {
      expect(ParamHelper.getBoolTrue(null)).toBe(true)
    })
  })

  describe('getBoolFalse', () => {
    it('should return false for falsy values', () => {
      expect(ParamHelper.getBoolFalse(false)).toBe(false)
      expect(ParamHelper.getBoolFalse(0)).toBe(false)
      expect(ParamHelper.getBoolFalse('false')).toBe(false)
    })

    it('should return true for truthy values', () => {
      expect(ParamHelper.getBoolFalse(true)).toBe(true)
      expect(ParamHelper.getBoolFalse(1)).toBe(true)
      expect(ParamHelper.getBoolFalse('true')).toBe(true)
    })

    it('should return false for null value', () => {
      expect(ParamHelper.getBoolFalse(null)).toBe(false)
    })
  })

  describe('isNotFoundError', () => {
    it('should return true if the error code is "NOT_FOUND"', () => {
      const error = {
        code: 'NOT_FOUND',
      }

      expect(ParamHelper.isNotFoundError(error)).toBe(true)
    })

    it('should return false if the error code is not "NOT_FOUND"', () => {
      const error = {
        code: 'INTERNAL_SERVER_ERROR',
      }

      expect(ParamHelper.isNotFoundError(error)).toBe(false)
    })
  })

  describe('isChangeWithFalse', () => {
    it('should return true if the value changes from true to false', () => {
      expect(ParamHelper.isChangeWithFalse(false, true)).toBe(true)
    })

    it('should return false if the value does not change or changes to true', () => {
      expect(ParamHelper.isChangeWithFalse(true, true)).toBe(false)
      expect(ParamHelper.isChangeWithFalse(false, false)).toBe(false)
      expect(ParamHelper.isChangeWithFalse(true, false)).toBe(false)
    })
  })

  describe('isChangeWithTrue', () => {
    it('should return true if the value changes from false to true', () => {
      expect(ParamHelper.isChangeWithTrue(true, false)).toBe(true)
    })

    it('should return false if the value does not change or changes to false', () => {
      expect(ParamHelper.isChangeWithTrue(false, false)).toBe(false)
      expect(ParamHelper.isChangeWithTrue(true, true)).toBe(false)
      expect(ParamHelper.isChangeWithTrue(false, true)).toBe(false)
    })
  })
})
