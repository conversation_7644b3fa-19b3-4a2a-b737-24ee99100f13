import { describe, it, expect } from 'vitest'
import { StringHelper } from './StringHelper'

describe('StringHelper', () => {
  describe('genString', () => {
    it('should generate a random string of the specified length', () => {
      const length = 10
      const result = StringHelper.genString(length)

      expect(result).toHaveLength(length)
      expect(typeof result).toBe('string')
    })

    it('should generate a random string of default length if not specified', () => {
      const result = StringHelper.genString()

      expect(result).toHaveLength(5)
      expect(typeof result).toBe('string')
    })
  })

  describe('split', () => {
    it('should split a string into an array based on the specified separator', () => {
      const str = 'apple,banana,orange'
      const separator = ','
      const result = StringHelper.split(str, separator)

      expect(result).toEqual(['apple', 'banana', 'orange'])
    })

    it('should return an empty array for null or undefined string', () => {
      expect(StringHelper.split(null, ',')).toEqual([])
      expect(StringHelper.split(undefined, ',')).toEqual([])
    })
  })

  describe('joinURL', () => {
    it('should join the provided URL parts', () => {
      const result = StringHelper.joinURL('https://example.com', 'path', 'to', 'resource')

      expect(result).toBe('https://example.com/path/to/resource')
    })
  })

  describe('truncate', () => {
    it('should truncate a string if it exceeds the specified length', () => {
      const str = 'This is a long string that needs to be truncated'
      const result = StringHelper.truncate(str, 20)

      expect(result).toBe('This is a long strin...')
    })

    it('should return the original string if it does not exceed the specified length', () => {
      const str = 'Short string'
      const result = StringHelper.truncate(str, 20)

      expect(result).toBe('Short string')
    })
  })

  describe('getError', () => {
    it('should return the error message from the errorData', () => {
      const errorData = {
        code: 'ERROR_CODE',
        message: 'Error message',
      }

      const result = StringHelper.getError(errorData)

      expect(result).toBe('Error message')
    })

    it('should return the default error message if the errorData is empty', () => {
      const result = StringHelper.getError({}, 'Default error message')

      expect(result).toBe('Default error message')
    })
  })
})
