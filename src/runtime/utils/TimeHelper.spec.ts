import { assert, describe, it, vi } from 'vitest'
import { TimeHelper } from './TimeHelper' // Adjust the import path as needed

vi.mock('#core/composables/useConfig', () => ({
  useCoreConfig: () => ({
    date_format: 'yyyy-MM-dd',
    date_time_format: 'yyyy-MM-dd HH:mm',
    date_format_system: 'yyyy-MM-dd',
    date_time_format_system: 'yyyy-MM-dd HH:mm',
    time_format: 'HH:mm',
    is_thai_year: false,
    time_zone: 'Asia/Bangkok',
  }),
}))

describe('TimeHelper', async () => {
  it('toUTC should convert time to UTC format', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutput = '2023-12-19 05:00' // Adjust the expected output as needed

    const result = TimeHelper.toUTC(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('toUTC should convert time to UTC format 2', () => {
    const inputTime = '2023-01-16T22:01:00Z' // Adjust the input time as needed
    const expectedOutput = '2023-01-16 22:01' // Adjust the expected output as needed

    const result = TimeHelper.toUTC(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('toLocal should convert time to local format', () => {
    const inputTime = '2023-01-16T22:00:00Z' // Adjust the input time as needed
    const expectedOutput = '2023-01-17 05:00' // Adjust the expected output as needed

    const result = TimeHelper.toLocal(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('[thai year ]toLocal should convert time to local format', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutput = '2023-12-19 12:00' // Adjust the expected output as needed

    const result = TimeHelper.toLocal(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('getCurrentDate should return the current date in yyyy-MM-dd format', () => {
    const expectedOutputRegex = /^\d{4}-\d{2}-\d{2}$/

    const result = TimeHelper.getCurrentDate()

    assert.match(result, expectedOutputRegex)
  })

  it('getDateFormTime should convert time to date format', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutput = '2023-12-19' // Adjust the expected output as needed

    const result = TimeHelper.getDateFormTime(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('getDateFormTimeWithLocal should convert time to date format with local time', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutputRegex = /^\d{4}-\d{2}-\d{2}$/

    const result = TimeHelper.getDateFormTimeWithLocal(inputTime)

    assert.match(result, expectedOutputRegex)
  })

  it('getISODateTimeFormTime should convert time to ISO date-time format', () => {
    const inputTime = '2023-12-19 14:30'
    const expectedOutput = '2023-12-19T14:30:00+07:00'

    const result = TimeHelper.getISODateTimeFormTime(inputTime)

    assert.equal(result, expectedOutput)
  })

  it('getDateTimeFormTime should convert time to date-time format', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutputRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/

    const result = TimeHelper.getDateTimeFormTime(inputTime)

    assert.match(result, expectedOutputRegex)
  })

  it('getTimeFormTime should convert time to time format', () => {
    const inputTime = '2023-12-19 12:00' // Adjust the input time as needed
    const expectedOutputRegex = /^\d{2}:\d{2}$/

    const result = TimeHelper.getTimeFormTime(inputTime)

    assert.match(result, expectedOutputRegex)
  })

  it('getCurrentDateTime should return the current date and time in yyyy-MM-dd HH:mm format', () => {
    const expectedOutputRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/

    const result = TimeHelper.getCurrentDateTime()

    assert.match(result, expectedOutputRegex)
  })
})
