import { addYears, format, formatISO, isDate, isValid, parse } from 'date-fns'
import * as locales from 'date-fns/locale'
import { formatInTimeZone, type FormatOptionsWithTZ } from 'date-fns-tz'
import { useCoreConfig } from '#core/composables/useConfig'

const dateFormat = useCoreConfig().date_format_system
const dateFormatDisplay = useCoreConfig().date_format
const dateTimeFormat = useCoreConfig().date_time_format_system
const dateTimeFormatDisplay = useCoreConfig().date_time_format
const timeFormat = useCoreConfig().time_format
const isThaiYear = useCoreConfig().is_thai_year
const isThaiMonth = useCoreConfig().is_thai_month
const timeZone = useCoreConfig().time_zone

const getTime = (time: string | Date): Date => {
  return isDate(time) ? time : new Date(time)
}

export class TimeHelper {
  static thaiFormat = (dateStr: Date, formatStr: string): string => {
    let newDateStr = dateStr
    const options: FormatOptionsWithTZ = {}

    if (isThaiYear) {
      newDateStr = addYears(dateStr, 543)
    }

    if (isThaiMonth) {
      options.locale = locales.th
    }

    return formatInTimeZone(newDateStr, timeZone, formatStr, options)
  }

  static displayDate = (time: string | Date): string => {
    if (!time) {
      return time
    }

    const parsedTime = getTime(time)

    const newTime = TimeHelper.thaiFormat(parsedTime, dateFormatDisplay)

    return isValid(parsedTime) ? newTime : (time as string)
  }

  static displayDateTime = (time: string | Date): string => {
    if (!time) {
      return time
    }

    const parsedTime = getTime(time)

    const newTime = TimeHelper.thaiFormat(parsedTime, dateTimeFormatDisplay)

    return isValid(parsedTime) ? newTime : (time as string)
  }

  static displayDateRange = (
    startDate: Date | string,
    endDate: Date | string,
  ): {
    startDate: Date | string
    endDate: Date | string
  } => {
    if (!startDate || !endDate) {
      return {
        startDate,
        endDate,
      }
    }

    const parsedStartDate = getTime(startDate)
    const parsedEndDate = getTime(endDate)

    const newStartDate = TimeHelper.thaiFormat(parsedStartDate, dateFormatDisplay)
    const newEndDate = TimeHelper.thaiFormat(parsedEndDate, dateFormatDisplay)

    return {
      startDate: isValid(parsedStartDate) ? newStartDate : (startDate as string),
      endDate: isValid(parsedEndDate) ? newEndDate : (endDate as string),
    }
  }

  static toUTC = (time: string | Date): string => {
    if (!time) {
      return time
    }

    try {
      const parsedTime = getTime(time)
      const newTime = formatInTimeZone(parsedTime, 'Zulu', dateTimeFormat)

      return isValid(parsedTime) ? newTime : (time as string)
    } catch (e) {
      return (time as string).toString()
    }
  }

  static toLocal = (time: string | Date): string => {
    if (!time) {
      return time
    }

    try {
      const parsedTime = getTime(time)

      const newTime = formatInTimeZone(parsedTime, timeZone, dateTimeFormat)

      return isValid(parsedTime) ? newTime : (time as string)
    } catch (e) {
      return (time as string).toString()
    }
  }

  static getCurrentDate = (): string => {
    return format(new Date(), dateFormat)
  }

  static getDateFormTime = (time: string | Date): string => {
    if (!time) {
      return time
    }

    const parsedTime = isDate(time) ? time : parse(time, dateTimeFormat, new Date())

    const newTime = format(parsedTime, dateFormat)

    return isValid(parsedTime) ? newTime : (time as string)
  }

  static getDateFormTimeWithLocal = (time: string | Date): string => {
    if (!time) {
      return time
    }

    const localTime = TimeHelper.toLocal(time)
    const parsedTime = isDate(localTime) ? localTime : parse(localTime, dateTimeFormat, new Date())

    const newTime = format(parsedTime as any, dateFormat)

    return isValid(parsedTime) ? newTime : (time as string)
  }

  static getISODateTimeFormTime = (time: string | Date): string => {
    if (!time) {
      return time
    }

    try {
      const parsedTime = isDate(time) ? time : parse(time, dateTimeFormat, new Date())

      const testTime = format(parsedTime, dateTimeFormat)

      if (!isValid(parsedTime) || testTime === 'Invalid Date') {
        return time as string
      }

      return formatISO(parsedTime)
    } catch (e) {
      return (time as string).toString()
    }
  }

  static getDateTimeFormTime = (time: string | Date): string => {
    if (!time) {
      return time
    }

    const parsedTime = isDate(time) ? time : parse(time, dateTimeFormat, new Date())

    const newTime = format(parsedTime, dateTimeFormat)

    return isValid(parsedTime) ? newTime : (time as string)
  }

  static getTimeFormTime = (time: string | Date): string => {
    if (!time) {
      return time
    }

    const parsedTime = isDate(time) ? time : parse(time, dateTimeFormat, new Date())

    const newTime = format(parsedTime, timeFormat)

    return isValid(parsedTime) ? newTime : (time as string)
  }

  static getCurrentDateTime = (): string => {
    return format(new Date(), dateTimeFormat)
  }
}
