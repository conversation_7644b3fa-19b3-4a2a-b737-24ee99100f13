import { describe, expect, it } from 'vitest'
import { _isEmpty, _get } from './lodash'

describe('lodash utilities', () => {
  describe('_isEmpty', () => {
    it('should return true for null and undefined', () => {
      expect(_isEmpty(null)).toBe(true)
      expect(_isEmpty(undefined)).toBe(true)
    })

    it('should return true for empty arrays and strings', () => {
      expect(_isEmpty([])).toBe(true)
      expect(_isEmpty('')).toBe(true)
    })

    it('should return false for non-empty arrays and strings', () => {
      expect(_isEmpty([1, 2])).toBe(false)
      expect(_isEmpty('text')).toBe(false)
    })

    it('should return true for empty objects', () => {
      expect(_isEmpty({})).toBe(true)
    })

    it('should return false for non-empty objects', () => {
      expect(_isEmpty({
        a: 1,
      })).toBe(false)
    })

    it('should return true for empty Maps and Sets', () => {
      expect(_isEmpty(new Map())).toBe(true)
      expect(_isEmpty(new Set())).toBe(true)
    })

    it('should return false for numbers and booleans', () => {
      expect(_isEmpty(0)).toBe(false)
      expect(_isEmpty(123)).toBe(false)
      expect(_isEmpty(true)).toBe(false)
      expect(_isEmpty(false)).toBe(false)
    })
  })

  describe('_get', () => {
    const object = {
      a: [{
        b: {
          c: 3,
        },
      }],
      x: {
        y: null,
        z: undefined,
      },
      arr: [10, 20, {
        val: 30,
      }],
    }

    it('should _get a value using a string path', () => {
      expect(_get(object, 'a[0].b.c')).toBe(3)
      expect(_get(object, 'a["0"].b.c')).toBe(3)
    })

    it('should _get a value using an array path', () => {
      expect(_get(object, ['a', 0, 'b', 'c'])).toBe(3)
    })

    it('should return undefined for non-existent paths', () => {
      expect(_get(object, 'a.b.c.d')).toBeUndefined()
      expect(_get(object, ['x', 'y', 'z'])).toBeUndefined() // x.y is null
    })

    it('should return default value for non-existent paths if provided', () => {
      expect(_get(object, 'a.b.c.d', 'default')).toBe('default')
      expect(_get(object, ['x', 'y', 'z'], 100)).toBe(100)
    })

    it('should return default value for null or undefined object', () => {
      expect(_get(null, 'a.b', 'default')).toBe('default')
      expect(_get(undefined, ['a', 'b'], 'default')).toBe('default')
    })

    it('should handle paths with array indices', () => {
      expect(_get(object, 'arr[1]')).toBe(20)
      expect(_get(object, 'arr[2].val')).toBe(30)
      expect(_get(object, ['arr', 0])).toBe(10)
    })

    it('should return undefined for out-of-bounds array indices', () => {
      expect(_get(object, 'arr[5]')).toBeUndefined()
    })

    it(
      'should return default value for out-of-bounds array indices if provided',
      () => {
        expect(_get(object, 'arr[5]', 'default')).toBe('default')
      })

    it('should handle paths leading to null or undefined values in the object',
      () => {
        expect(_get(object, 'x.y')).toBeNull() // x.y is null
        expect(_get(object, 'x.z')).toBeUndefined() // x.z is undefined
      })

    it('should return default value when path leads to undefined', () => {
      expect(_get(object, 'x.z', 'default')).toBe('default')
    })

    it('should return null when path leads to null, even with a default value',
      () => {
        expect(_get(object, 'x.y', 'default')).toBeNull()
      })

    it('should handle empty path string', () => {
      expect(_get(object, '')).toEqual(object)
      expect(_get(object, '', 'default')).toBe(object)
    })

    it('should handle empty path array', () => {
      expect(_get(object, [])).toEqual(object) // This behavior might differ from lodash, which returns the object itself.
      expect(_get(object, [], 'default')).toEqual(object) // Adjust based on desired behavior for empty array path.
    })
  })
})
