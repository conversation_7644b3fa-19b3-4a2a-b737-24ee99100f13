export const _isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) {
    return true
  } else if (Array.isArray(value) || typeof value === 'string') {
    return value.length === 0
  } else if (typeof value === 'object') {
    return Object.keys(value).length === 0
  } else if (typeof value.size === 'number') {
    // For Maps and Sets
    return value.size === 0
  }

  return false // Assuming other data types are not considered empty
}

/**
 * Gets the value at path of object. If the resolved value is undefined,
 * the defaultValue is returned in its place.
 */
// Type-safe path inference
type PathKeys<T> = {
  [K in keyof T]: T[K] extends object
    ? K extends string | number
      ? `${K}` | `${K}.${PathKeys<T[K]>}`
      : never
    : K extends string | number
      ? `${K}`
      : never;
}[keyof T]

// Main get function with type-safe path inference
interface GetFunction {
  // Type-safe overloads with path inference
  <T extends object, K extends PathKeys<T>>(object: T, path: K): any
  <T extends object, K extends PathKeys<T>, D>(object: T, path: K, defaultValue: D): D
  // Fallback overloads for non-inferred paths
  <T>(object: any, path: string | (string | number)[], defaultValue: T): T
  <T = any>(object: any, path: string | (string | number)[]): T | undefined
}

export const _get: GetFunction = <T = any>(
  object: any,
  path: string | (string | number)[],
  defaultValue?: T,
): T | undefined => {
  // Handle null/undefined object
  if (object == null) {
    return defaultValue
  }

  // Convert path to array if it's a string
  const pathArray = Array.isArray(path) ? path : stringToPath(path)

  // Traverse the object following the path
  let current: any = object

  for (let i = 0; i < pathArray.length; i++) {
    if (current == null) {
      return defaultValue
    }

    current = current[pathArray[i]]
  }

  // Return defaultValue if result is undefined
  return current === undefined ? defaultValue : current
}

/**
 * Converts a string path to an array of keys
 * Handles bracket notation and dot notation
 */
const stringToPath = (path: string): (string | number)[] => {
  if (typeof path !== 'string') {
    return [path]
  }

  // Replace bracket notation with dot notation, then split
  const normalizedPath = path
    .replace(/\[(\d+)\]/g, '.$1') // [0] -> .0
    .replace(/\[['"]([^'"]*)['"]\]/g, '.$1') // ['key'] -> .key
    .replace(/^\./, '') // Remove leading dot

  if (normalizedPath === '') {
    return []
  }

  return normalizedPath.split('.').map((key) => {
    // Convert numeric strings to numbers
    const numKey = Number(key)

    return !Number.isNaN(numKey) && key === numKey.toString() ? numKey : key
  })
}
